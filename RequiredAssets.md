# Velocity Rush - Required Assets List

## 1. 3D Models & Meshes

### 1.1 Vehicles (6 Cars Total)
#### Starter Cars (Free)
- **Compact Racer** - Low-poly sports car (~2000 triangles)
- **Street Cruiser** - Mid-size racing vehicle (~2500 triangles)
- **Speed Demon** - Aerodynamic race car (~3000 triangles)

#### Premium Cars (IAP)
- **Cyber Beast** - Futuristic supercar (~3500 triangles)
- **Thunder Bolt** - Electric racing machine (~3000 triangles)
- **Neon Runner** - Glowing street racer (~2800 triangles)

**Technical Specs per Car:**
- LOD0: Full detail (main model)
- LOD1: 50% triangle reduction (medium distance)
- LOD2: 75% triangle reduction (far distance)
- Separate wheel meshes for rotation
- Damage states (optional: 3 levels)

### 1.2 Track Elements
#### Core Track Pieces
- **Straight sections** (100m, 200m, 500m)
- **Curves** (30°, 45°, 90°, 180°)
- **Elevation changes** (ramps, hills, dips)
- **Start/finish line** with timing gates
- **Checkpoints** (invisible collision triggers)

#### Track Decorations
- **Barriers** (safety rails, concrete walls)
- **Grandstands** (spectator areas)
- **Buildings** (futuristic cityscape)
- **Vegetation** (trees, bushes - low poly)
- **Lighting** (street lamps, neon signs)

### 1.3 Power-up Models
- **Nitro Boost** - Glowing blue cylinder
- **Oil Spill** - Dark liquid barrel
- **Speed Boost** - Lightning bolt icon
- **Shield** - Protective dome effect
- **EMP** - Electric orb with sparks

### 1.4 Environment Props
- **Skyboxes** (4 different times/weather)
- **Particle systems** (dust, sparks, smoke)
- **Track surfaces** (asphalt, concrete, metal)
- **Sponsor banners** (customizable textures)

## 2. Textures & Materials

### 2.1 Vehicle Textures (Per Car)
- **Diffuse maps** (2048x2048 base color)
- **Normal maps** (detail and surface normals)
- **Metallic/Roughness** (PBR workflow)
- **Emission maps** (headlights, taillights)
- **Customization masks** (for color variations)

### 2.2 Track Textures
- **Road surfaces** (asphalt, concrete, dirt)
- **Barrier materials** (metal, concrete, plastic)
- **Building textures** (glass, metal, neon)
- **Ground textures** (grass, sand, gravel)
- **Decal textures** (arrows, numbers, logos)

### 2.3 UI Textures
- **Button states** (normal, pressed, disabled)
- **Icons** (power-ups, currency, settings)
- **Backgrounds** (menu gradients, patterns)
- **Progress bars** (health, boost, loading)

## 3. Audio Assets

### 3.1 Sound Effects
#### Vehicle Audio
- **Engine sounds** (6 different engines, looping)
- **Tire screeching** (turning, braking)
- **Collision sounds** (car-to-car, wall impacts)
- **Gear shifting** (acceleration audio)

#### Power-up Audio
- **Pickup sounds** (item collection)
- **Activation sounds** (power-up usage)
- **Impact effects** (oil spill, EMP hit)
- **Boost audio** (nitro, speed boost)

#### Environmental Audio
- **Wind effects** (speed-based intensity)
- **Crowd cheering** (race events)
- **Ambient track sounds** (city, nature)
- **Countdown timer** (3-2-1-GO!)

#### UI Audio
- **Button clicks** (menu navigation)
- **Notification sounds** (achievements, messages)
- **Victory/defeat** (race completion)
- **Currency sounds** (coin collection)

### 3.2 Music Tracks
- **Main menu theme** (2-3 minute loop)
- **Race music** (4-5 high-energy tracks)
- **Garage/customization** (calm background)
- **Victory fanfare** (15-30 second stinger)
- **Defeat sound** (10-15 second stinger)

## 4. UI Elements

### 4.1 HUD Components
- **Speedometer** (circular or linear)
- **Position indicator** (1st, 2nd, etc.)
- **Lap counter** (current/total laps)
- **Minimap** (track overview with positions)
- **Power-up slot** (current item display)
- **Boost meter** (nitro availability)

### 4.2 Menu Interfaces
#### Main Menu
- **Play button** (quick match)
- **Garage button** (car customization)
- **Settings button** (options menu)
- **Leaderboard** (rankings display)
- **Store button** (IAP interface)

#### Race Lobby
- **Player list** (names, cars, ready status)
- **Track preview** (image and info)
- **Chat system** (quick messages)
- **Ready/Start buttons**

#### Results Screen
- **Final positions** (1st-6th place)
- **Race statistics** (best lap, top speed)
- **Rewards earned** (coins, XP, trophies)
- **Next race button**

### 4.3 Customization UI
- **Car selection** (carousel or grid)
- **Color picker** (HSV wheel or swatches)
- **Upgrade panels** (speed, handling, acceleration)
- **Purchase confirmations** (IAP dialogs)

## 5. Animation Assets

### 5.1 Vehicle Animations
- **Wheel rotation** (speed-based)
- **Suspension movement** (bump reactions)
- **Steering wheel** (turn input response)
- **Damage animations** (smoke, sparks)

### 5.2 UI Animations
- **Button hover/press** (scale, color changes)
- **Screen transitions** (slide, fade effects)
- **Loading animations** (spinners, progress bars)
- **Notification popups** (achievement unlocks)

### 5.3 Effect Animations
- **Power-up pickups** (rotation, glow pulse)
- **Particle systems** (exhaust, dust, sparks)
- **Track elements** (moving barriers, flags)
- **Environmental** (swaying trees, flowing water)

## 6. Shader Assets

### 6.1 Vehicle Shaders
- **Car paint shader** (metallic, pearl, matte finishes)
- **Glass shader** (windows with reflections)
- **Tire rubber shader** (realistic surface)
- **Chrome/metal shader** (highly reflective parts)

### 6.2 Track Shaders
- **Road surface shader** (wet/dry variations)
- **Holographic shader** (futuristic elements)
- **Emission shader** (neon lights, signs)
- **Transparent shader** (glass barriers, water)

### 6.3 Effect Shaders
- **Particle shaders** (smoke, fire, sparks)
- **Distortion shader** (heat waves, speed lines)
- **Outline shader** (power-up highlights)
- **Dissolve shader** (teleport effects)

## 7. Configuration Files

### 7.1 Game Data
- **Car statistics** (JSON: speed, handling, acceleration)
- **Track configurations** (waypoints, spawn points)
- **Power-up properties** (duration, effects, cooldowns)
- **Progression tables** (XP requirements, unlocks)

### 7.2 Localization
- **Text strings** (English, Spanish, French, German)
- **Audio localization** (announcer voices)
- **Cultural adaptations** (currency symbols, date formats)

## 8. Asset Optimization Guidelines

### 8.1 Mobile Optimization
- **Texture compression** (ASTC for Android, PVRTC for iOS)
- **Mesh optimization** (LOD system, occlusion culling)
- **Audio compression** (OGG Vorbis, appropriate bitrates)
- **Shader variants** (mobile-optimized versions)

### 8.2 File Size Targets
- **Total app size** < 500MB initial download
- **Individual textures** < 2MB compressed
- **Audio files** < 1MB per sound effect
- **3D models** < 500KB per LOD level

### 8.3 Performance Targets
- **Draw calls** < 100 per frame
- **Triangle count** < 50K total on screen
- **Texture memory** < 200MB on device
- **Audio channels** < 32 simultaneous sounds
