using UnityEngine;
using Fusion;
using Fusion.Sockets;
using System;
using System.Collections.Generic;
using UnityEngine.SceneManagement;

namespace VelocityRush.Network
{
    public class NetworkManager : MonoBehaviour, INetworkBehaviour
    {
        public static NetworkManager Instance { get; private set; }
        
        [Header("Network Settings")]
        public string gameVersion = "1.0";
        public string roomNamePrefix = "VelocityRush_";
        
        [Header("Prefabs")]
        public NetworkPrefabRef playerCarPrefab;
        
        private NetworkRunner _runner;
        private bool _isHost = false;
        
        public event Action OnConnectedToServer;
        public event Action OnDisconnectedFromServer;
        public event Action<List<SessionInfo>> OnRoomListUpdated;
        public event Action OnJoinedRoom;
        public event Action OnLeftRoom;
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            InitializeNetwork();
        }
        
        private void InitializeNetwork()
        {
            // Initialize Fusion runner
            if (_runner == null)
            {
                _runner = gameObject.AddComponent<NetworkRunner>();
            }
        }
        
        public async void CreateRoom(string roomName = null)
        {
            if (_runner == null) return;
            
            if (string.IsNullOrEmpty(roomName))
            {
                roomName = roomNamePrefix + UnityEngine.Random.Range(1000, 9999);
            }
            
            _isHost = true;
            
            var args = new StartGameArgs()
            {
                GameMode = GameMode.Host,
                SessionName = roomName,
                Scene = SceneManager.GetActiveScene().buildIndex,
                SceneManager = gameObject.AddComponent<NetworkSceneManagerDefault>()
            };
            
            await _runner.StartGame(args);
        }
        
        public async void JoinRoom(string roomName)
        {
            if (_runner == null) return;
            
            _isHost = false;
            
            var args = new StartGameArgs()
            {
                GameMode = GameMode.Client,
                SessionName = roomName,
                Scene = SceneManager.GetActiveScene().buildIndex,
                SceneManager = gameObject.AddComponent<NetworkSceneManagerDefault>()
            };
            
            await _runner.StartGame(args);
        }
        
        public async void JoinRandomRoom()
        {
            if (_runner == null) return;
            
            _isHost = false;
            
            var args = new StartGameArgs()
            {
                GameMode = GameMode.AutoHostOrClient,
                SessionName = roomNamePrefix + "Random",
                Scene = SceneManager.GetActiveScene().buildIndex,
                SceneManager = gameObject.AddComponent<NetworkSceneManagerDefault>()
            };
            
            await _runner.StartGame(args);
        }
        
        public void LeaveRoom()
        {
            if (_runner != null && _runner.IsRunning)
            {
                _runner.Shutdown();
            }
        }
        
        public void OnPlayerJoined(NetworkRunner runner, PlayerRef player)
        {
            Debug.Log($"Player {player} joined the room");
            
            // Spawn player car if we're in the race scene
            if (SceneManager.GetActiveScene().name == "RaceTrack" && runner.IsHost)
            {
                SpawnPlayerCar(runner, player);
            }
        }
        
        public void OnPlayerLeft(NetworkRunner runner, PlayerRef player)
        {
            Debug.Log($"Player {player} left the room");
        }
        
        private void SpawnPlayerCar(NetworkRunner runner, PlayerRef player)
        {
            // Find spawn point for this player
            Vector3 spawnPosition = GetSpawnPosition(player);
            Quaternion spawnRotation = Quaternion.identity;
            
            // Spawn the networked car
            NetworkObject playerCar = runner.Spawn(playerCarPrefab, spawnPosition, spawnRotation, player);
            
            Debug.Log($"Spawned car for player {player} at position {spawnPosition}");
        }
        
        private Vector3 GetSpawnPosition(PlayerRef player)
        {
            // Simple spawn positioning - can be improved with actual spawn points
            int playerIndex = player.PlayerId;
            float spacing = 5f;
            
            return new Vector3(playerIndex * spacing, 1f, 0f);
        }
        
        public bool IsConnected => _runner != null && _runner.IsRunning;
        public bool IsHost => _isHost && IsConnected;
        public int PlayerCount => _runner != null ? _runner.ActivePlayers.Count : 0;
        
        public void OnInput(NetworkRunner runner, NetworkInputData input)
        {
            var inputHandler = FindObjectOfType<FusionInputHandler>();
            if (inputHandler != null)
            {
                inputHandler.OnInput(runner, input);
            }
        }
        
        public void OnInputMissing(NetworkRunner runner, PlayerRef player, NetworkInputData input)
        {
            // Handle missing input
        }
        
        public void OnShutdown(NetworkRunner runner, ShutdownReason shutdownReason)
        {
            Debug.Log($"Network shutdown: {shutdownReason}");
            OnDisconnectedFromServer?.Invoke();
        }
        
        public void OnConnectedToServer(NetworkRunner runner)
        {
            Debug.Log("Connected to server");
            OnConnectedToServer?.Invoke();
        }
        
        public void OnDisconnectedFromServer(NetworkRunner runner)
        {
            Debug.Log("Disconnected from server");
            OnDisconnectedFromServer?.Invoke();
        }
        
        public void OnConnectRequest(NetworkRunner runner, NetworkRunnerCallbackArgs.ConnectRequest request, byte[] token)
        {
            // Accept all connection requests for now
            request.Accept();
        }
        
        public void OnConnectFailed(NetworkRunner runner, NetAddress remoteAddress, NetConnectFailedReason reason)
        {
            Debug.LogError($"Connection failed: {reason}");
        }
        
        public void OnUserSimulationMessage(NetworkRunner runner, SimulationMessagePtr message)
        {
            // Handle custom messages
        }
        
        public void OnSessionListUpdated(NetworkRunner runner, List<SessionInfo> sessionList)
        {
            OnRoomListUpdated?.Invoke(sessionList);
        }
        
        public void OnCustomAuthenticationResponse(NetworkRunner runner, Dictionary<string, object> data)
        {
            // Handle authentication response
        }
        
        public void OnHostMigration(NetworkRunner runner, HostMigrationToken hostMigrationToken)
        {
            // Handle host migration
        }
        
        public void OnReliableDataReceived(NetworkRunner runner, PlayerRef player, ArraySegment<byte> data)
        {
            // Handle reliable data
        }
        
        public void OnSceneLoadDone(NetworkRunner runner)
        {
            Debug.Log("Scene load completed");
        }
        
        public void OnSceneLoadStart(NetworkRunner runner)
        {
            Debug.Log("Scene load started");
        }
    }
}
