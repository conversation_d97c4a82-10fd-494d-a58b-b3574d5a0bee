using UnityEngine;
using Fusion;

namespace VelocityRush.PowerUps
{
    public enum PowerUpType
    {
        NitroBoost,
        SpeedBoost,
        Shield
    }
    
    public class PowerUp : NetworkBehaviour
    {
        [Header("Power-up Settings")]
        public PowerUpType powerUpType = PowerUpType.NitroBoost;
        public float respawnTime = 10f;
        public float rotationSpeed = 90f;
        public float bobSpeed = 2f;
        public float bobHeight = 0.5f;
        
        [Header("Visual Effects")]
        public GameObject visualModel;
        public ParticleSystem collectEffect;
        public AudioClip collectSound;
        
        [Networked] public bool IsCollected { get; set; }
        [Networked] public float RespawnTimer { get; set; }
        
        private Vector3 _startPosition;
        private AudioSource _audioSource;
        
        public override void Spawned()
        {
            _startPosition = transform.position;
            _audioSource = GetComponent<AudioSource>();
            
            if (Object.HasStateAuthority)
            {
                IsCollected = false;
                RespawnTimer = 0f;
            }
        }
        
        public override void FixedUpdateNetwork()
        {
            if (Object.HasStateAuthority)
            {
                // Handle respawn timer
                if (IsCollected && RespawnTimer > 0f)
                {
                    RespawnTimer -= Runner.DeltaTime;
                    if (RespawnTimer <= 0f)
                    {
                        Respawn();
                    }
                }
            }
            
            // Visual updates
            if (!IsCollected)
            {
                UpdateVisuals();
            }
        }
        
        private void UpdateVisuals()
        {
            if (visualModel == null) return;
            
            // Rotate the power-up
            transform.Rotate(Vector3.up, rotationSpeed * Runner.DeltaTime);
            
            // Bob up and down
            float bobOffset = Mathf.Sin(Time.time * bobSpeed) * bobHeight;
            transform.position = _startPosition + Vector3.up * bobOffset;
        }
        
        [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
        public void Collect()
        {
            if (IsCollected) return;
            
            IsCollected = true;
            RespawnTimer = respawnTime;
            
            // Notify all clients about collection
            OnCollectedRPC();
        }
        
        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void OnCollectedRPC()
        {
            // Hide visual model
            if (visualModel != null)
            {
                visualModel.SetActive(false);
            }
            
            // Play collection effect
            if (collectEffect != null)
            {
                collectEffect.Play();
            }
            
            // Play collection sound
            if (_audioSource != null && collectSound != null)
            {
                _audioSource.PlayOneShot(collectSound);
            }
            
            Debug.Log($"Power-up {powerUpType} collected!");
        }
        
        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void Respawn()
        {
            IsCollected = false;
            
            // Show visual model
            if (visualModel != null)
            {
                visualModel.SetActive(true);
            }
            
            // Reset position
            transform.position = _startPosition;
            
            Debug.Log($"Power-up {powerUpType} respawned!");
        }
        
        private void OnTriggerEnter(Collider other)
        {
            if (IsCollected) return;
            
            var playerController = other.GetComponent<VelocityRush.Player.PlayerController>();
            if (playerController != null && playerController.Object.HasInputAuthority)
            {
                // Apply power-up effect to player
                ApplyPowerUpEffect(playerController);
                
                // Collect the power-up
                Collect();
            }
        }
        
        private void ApplyPowerUpEffect(VelocityRush.Player.PlayerController player)
        {
            switch (powerUpType)
            {
                case PowerUpType.NitroBoost:
                    ApplyNitroBoost(player);
                    break;
                    
                case PowerUpType.SpeedBoost:
                    ApplySpeedBoost(player);
                    break;
                    
                case PowerUpType.Shield:
                    ApplyShield(player);
                    break;
            }
        }
        
        private void ApplyNitroBoost(VelocityRush.Player.PlayerController player)
        {
            // Give player nitro if they don't have it
            if (player.IsNitroAvailable)
            {
                player.UseNitro();
            }
            else
            {
                // Reset nitro cooldown
                if (player.Object.HasStateAuthority)
                {
                    player.NetworkNitroCooldownTimer = 0f;
                }
            }
        }
        
        private void ApplySpeedBoost(VelocityRush.Player.PlayerController player)
        {
            // Apply temporary speed boost
            var speedBoost = player.GetComponent<SpeedBoostEffect>();
            if (speedBoost == null)
            {
                speedBoost = player.gameObject.AddComponent<SpeedBoostEffect>();
            }
            speedBoost.ApplySpeedBoost(5f, 1.5f); // 5 seconds, 1.5x speed
        }
        
        private void ApplyShield(VelocityRush.Player.PlayerController player)
        {
            // Apply temporary shield
            var shield = player.GetComponent<ShieldEffect>();
            if (shield == null)
            {
                shield = player.gameObject.AddComponent<ShieldEffect>();
            }
            shield.ApplyShield(8f); // 8 seconds of protection
        }
    }
    
    // Speed Boost Effect Component
    public class SpeedBoostEffect : MonoBehaviour
    {
        private VelocityRush.Player.CarMovement _carMovement;
        private float _originalMotorForce;
        private float _boostTimer;
        private float _speedMultiplier;
        
        private void Start()
        {
            _carMovement = GetComponent<VelocityRush.Player.CarMovement>();
            if (_carMovement != null)
            {
                _originalMotorForce = _carMovement.motorForce;
            }
        }
        
        private void Update()
        {
            if (_boostTimer > 0f)
            {
                _boostTimer -= Time.deltaTime;
                if (_boostTimer <= 0f)
                {
                    RemoveSpeedBoost();
                }
            }
        }
        
        public void ApplySpeedBoost(float duration, float multiplier)
        {
            if (_carMovement == null) return;
            
            _boostTimer = duration;
            _speedMultiplier = multiplier;
            _carMovement.motorForce = _originalMotorForce * multiplier;
            
            Debug.Log($"Speed boost applied: {multiplier}x for {duration} seconds");
        }
        
        private void RemoveSpeedBoost()
        {
            if (_carMovement != null)
            {
                _carMovement.motorForce = _originalMotorForce;
            }
            
            Debug.Log("Speed boost ended");
            Destroy(this);
        }
    }
    
    // Shield Effect Component
    public class ShieldEffect : MonoBehaviour
    {
        [Header("Shield Visual")]
        public GameObject shieldVisual;
        
        private float _shieldTimer;
        private bool _isShielded;
        
        private void Update()
        {
            if (_shieldTimer > 0f)
            {
                _shieldTimer -= Time.deltaTime;
                if (_shieldTimer <= 0f)
                {
                    RemoveShield();
                }
            }
        }
        
        public void ApplyShield(float duration)
        {
            _shieldTimer = duration;
            _isShielded = true;
            
            // Show shield visual
            if (shieldVisual != null)
            {
                shieldVisual.SetActive(true);
            }
            
            Debug.Log($"Shield applied for {duration} seconds");
        }
        
        private void RemoveShield()
        {
            _isShielded = false;
            
            // Hide shield visual
            if (shieldVisual != null)
            {
                shieldVisual.SetActive(false);
            }
            
            Debug.Log("Shield ended");
            Destroy(this);
        }
        
        public bool IsShielded()
        {
            return _isShielded && _shieldTimer > 0f;
        }
        
        private void OnCollisionEnter(Collision collision)
        {
            if (_isShielded)
            {
                // Reduce collision damage or effects
                var otherPlayer = collision.gameObject.GetComponent<VelocityRush.Player.PlayerController>();
                if (otherPlayer != null)
                {
                    Debug.Log("Shield absorbed collision damage!");
                }
            }
        }
    }
}
