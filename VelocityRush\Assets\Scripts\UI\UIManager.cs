using UnityEngine;
using UnityEngine.UI;
using VelocityRush.Core;
using VelocityRush.Network;

namespace VelocityRush.UI
{
    public class UIManager : MonoBehaviour
    {
        public static UIManager Instance { get; private set; }
        
        [Header("Main Menu UI")]
        public GameObject mainMenuPanel;
        public Button quickMatchButton;
        public Button createRoomButton;
        public Button joinRoomButton;
        public Button settingsButton;
        public Button quitButton;
        public InputField roomNameInput;
        
        [Header("Lobby UI")]
        public GameObject lobbyPanel;
        public Text roomNameText;
        public Text playerCountText;
        public Button startRaceButton;
        public Button leaveLobbyButton;
        public Transform playerListParent;
        public GameObject playerListItemPrefab;
        
        [Header("Loading UI")]
        public GameObject loadingPanel;
        public Text loadingText;
        
        [Header("Settings UI")]
        public GameObject settingsPanel;
        public Slider masterVolumeSlider;
        public Slider sfxVolumeSlider;
        public Slider musicVolumeSlider;
        public Button closeSettingsButton;
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeUI();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeUI()
        {
            // Setup button listeners
            if (quickMatchButton != null)
                quickMatchButton.onClick.AddListener(OnQuickMatchClicked);
                
            if (createRoomButton != null)
                createRoomButton.onClick.AddListener(OnCreateRoomClicked);
                
            if (joinRoomButton != null)
                joinRoomButton.onClick.AddListener(OnJoinRoomClicked);
                
            if (settingsButton != null)
                settingsButton.onClick.AddListener(OnSettingsClicked);
                
            if (quitButton != null)
                quitButton.onClick.AddListener(OnQuitClicked);
                
            if (startRaceButton != null)
                startRaceButton.onClick.AddListener(OnStartRaceClicked);
                
            if (leaveLobbyButton != null)
                leaveLobbyButton.onClick.AddListener(OnLeaveLobbyClicked);
                
            if (closeSettingsButton != null)
                closeSettingsButton.onClick.AddListener(OnCloseSettingsClicked);
            
            // Setup volume sliders
            if (masterVolumeSlider != null)
                masterVolumeSlider.onValueChanged.AddListener(OnMasterVolumeChanged);
                
            if (sfxVolumeSlider != null)
                sfxVolumeSlider.onValueChanged.AddListener(OnSFXVolumeChanged);
                
            if (musicVolumeSlider != null)
                musicVolumeSlider.onValueChanged.AddListener(OnMusicVolumeChanged);
            
            // Show main menu by default
            ShowMainMenu();
        }
        
        public void ShowMainMenu()
        {
            HideAllPanels();
            if (mainMenuPanel != null)
                mainMenuPanel.SetActive(true);
        }
        
        public void ShowLobby()
        {
            HideAllPanels();
            if (lobbyPanel != null)
                lobbyPanel.SetActive(true);
        }
        
        public void ShowLoading(string message = "Loading...")
        {
            if (loadingPanel != null)
            {
                loadingPanel.SetActive(true);
                if (loadingText != null)
                    loadingText.text = message;
            }
        }
        
        public void HideLoading()
        {
            if (loadingPanel != null)
                loadingPanel.SetActive(false);
        }
        
        public void ShowSettings()
        {
            if (settingsPanel != null)
                settingsPanel.SetActive(true);
        }
        
        public void HideSettings()
        {
            if (settingsPanel != null)
                settingsPanel.SetActive(false);
        }
        
        private void HideAllPanels()
        {
            if (mainMenuPanel != null) mainMenuPanel.SetActive(false);
            if (lobbyPanel != null) lobbyPanel.SetActive(false);
            if (loadingPanel != null) loadingPanel.SetActive(false);
            if (settingsPanel != null) settingsPanel.SetActive(false);
        }
        
        // Button Event Handlers
        private void OnQuickMatchClicked()
        {
            ShowLoading("Finding match...");
            NetworkManager.Instance.JoinRandomRoom();
        }
        
        private void OnCreateRoomClicked()
        {
            string roomName = roomNameInput != null ? roomNameInput.text : "";
            if (string.IsNullOrEmpty(roomName))
            {
                roomName = "Room_" + Random.Range(1000, 9999);
            }
            
            ShowLoading("Creating room...");
            NetworkManager.Instance.CreateRoom(roomName);
        }
        
        private void OnJoinRoomClicked()
        {
            string roomName = roomNameInput != null ? roomNameInput.text : "";
            if (string.IsNullOrEmpty(roomName))
            {
                Debug.LogWarning("Please enter a room name");
                return;
            }
            
            ShowLoading("Joining room...");
            NetworkManager.Instance.JoinRoom(roomName);
        }
        
        private void OnSettingsClicked()
        {
            ShowSettings();
        }
        
        private void OnQuitClicked()
        {
            GameManager.Instance.QuitGame();
        }
        
        private void OnStartRaceClicked()
        {
            if (NetworkManager.Instance.IsHost)
            {
                // Load race scene
                GameManager.Instance.LoadScene("RaceTrack");
            }
        }
        
        private void OnLeaveLobbyClicked()
        {
            NetworkManager.Instance.LeaveRoom();
            ShowMainMenu();
        }
        
        private void OnCloseSettingsClicked()
        {
            HideSettings();
        }
        
        // Volume Control Handlers
        private void OnMasterVolumeChanged(float value)
        {
            AudioListener.volume = value;
            PlayerPrefs.SetFloat("MasterVolume", value);
        }
        
        private void OnSFXVolumeChanged(float value)
        {
            // Set SFX volume (implement with audio manager)
            PlayerPrefs.SetFloat("SFXVolume", value);
        }
        
        private void OnMusicVolumeChanged(float value)
        {
            // Set music volume (implement with audio manager)
            PlayerPrefs.SetFloat("MusicVolume", value);
        }
        
        // Network Event Handlers
        private void OnEnable()
        {
            if (NetworkManager.Instance != null)
            {
                NetworkManager.Instance.OnConnectedToServer += OnConnectedToServer;
                NetworkManager.Instance.OnDisconnectedFromServer += OnDisconnectedFromServer;
                NetworkManager.Instance.OnJoinedRoom += OnJoinedRoom;
                NetworkManager.Instance.OnLeftRoom += OnLeftRoom;
            }
        }
        
        private void OnDisable()
        {
            if (NetworkManager.Instance != null)
            {
                NetworkManager.Instance.OnConnectedToServer -= OnConnectedToServer;
                NetworkManager.Instance.OnDisconnectedFromServer -= OnDisconnectedFromServer;
                NetworkManager.Instance.OnJoinedRoom -= OnJoinedRoom;
                NetworkManager.Instance.OnLeftRoom -= OnLeftRoom;
            }
        }
        
        private void OnConnectedToServer()
        {
            Debug.Log("Connected to server");
        }
        
        private void OnDisconnectedFromServer()
        {
            Debug.Log("Disconnected from server");
            HideLoading();
            ShowMainMenu();
        }
        
        private void OnJoinedRoom()
        {
            Debug.Log("Joined room");
            HideLoading();
            ShowLobby();
            UpdateLobbyUI();
        }
        
        private void OnLeftRoom()
        {
            Debug.Log("Left room");
            ShowMainMenu();
        }
        
        public void UpdateLobbyUI()
        {
            if (lobbyPanel == null || !lobbyPanel.activeInHierarchy) return;
            
            // Update room name
            if (roomNameText != null)
            {
                roomNameText.text = "Room: " + (NetworkManager.Instance.IsConnected ? "Connected" : "Disconnected");
            }
            
            // Update player count
            if (playerCountText != null)
            {
                int playerCount = NetworkManager.Instance.PlayerCount;
                int maxPlayers = GameManager.Instance.maxPlayersPerRoom;
                playerCountText.text = $"Players: {playerCount}/{maxPlayers}";
            }
            
            // Enable start button only for host
            if (startRaceButton != null)
            {
                startRaceButton.interactable = NetworkManager.Instance.IsHost;
            }
        }
        
        private void Update()
        {
            // Update lobby UI if in lobby
            if (lobbyPanel != null && lobbyPanel.activeInHierarchy)
            {
                UpdateLobbyUI();
            }
        }
        
        // Load saved settings
        private void Start()
        {
            LoadSettings();
        }
        
        private void LoadSettings()
        {
            if (masterVolumeSlider != null)
            {
                float masterVolume = PlayerPrefs.GetFloat("MasterVolume", 1f);
                masterVolumeSlider.value = masterVolume;
                AudioListener.volume = masterVolume;
            }
            
            if (sfxVolumeSlider != null)
            {
                float sfxVolume = PlayerPrefs.GetFloat("SFXVolume", 1f);
                sfxVolumeSlider.value = sfxVolume;
            }
            
            if (musicVolumeSlider != null)
            {
                float musicVolume = PlayerPrefs.GetFloat("MusicVolume", 1f);
                musicVolumeSlider.value = musicVolume;
            }
        }
    }
}
