# Velocity Rush - Multiplayer Racing Game

A complete Unity 3D multiplayer racing game built with Photon Fusion for real-time networking. Features up to 6 players racing on 3D tracks with power-ups, lap counting, and mobile-optimized controls.

## 🎮 Game Features

- **Real-time Multiplayer Racing** - Up to 6 players per race
- **Photon Fusion Networking** - Reliable real-time synchronization
- **Mobile-Optimized Controls** - Touch-friendly UI for Android & iOS
- **Power-up System** - Nitro Boost with cooldown mechanics
- **Lap Counting & Race Logic** - Complete race management system
- **Car Customization** - Color selection and multiple car models
- **Matchmaking** - Quick match, create room, and join room options
- **Cross-Platform** - Android and iOS support

## 🛠️ Technical Requirements

### Unity Version
- **Unity 2022.3 LTS** or newer
- **Universal Render Pipeline (URP)**
- **IL2CPP Scripting Backend**

### Required Packages
- **Photon Fusion SDK** (Latest version)
- **Unity Input System** (1.7.0+)
- **Unity Addressables** (1.21.14+)
- **TextMeshPro** (Built-in)

### Platform Requirements
- **Android:** API Level 24+ (Android 7.0), ARM64
- **iOS:** iOS 12.0+, ARM64
- **Minimum RAM:** 3GB
- **Target Frame Rate:** 60 FPS

## 📁 Project Structure

```
VelocityRush/
├── Assets/
│   ├── Scripts/
│   │   ├── Core/                 # GameManager, core systems
│   │   ├── Network/              # Photon Fusion networking
│   │   ├── Player/               # Car movement, player controller
│   │   ├── Race/                 # Race management, lap counting
│   │   ├── UI/                   # User interface, mobile input
│   │   ├── PowerUps/             # Power-up system
│   │   ├── Cars/                 # Car configurations
│   │   ├── Track/                # Track building tools
│   │   ├── Audio/                # Audio management
│   │   └── Scenes/               # Scene setup scripts
│   ├── Prefabs/                  # Game object prefabs
│   ├── Scenes/                   # Game scenes
│   ├── Resources/                # Runtime resources
│   └── StreamingAssets/          # Platform-specific assets
├── Packages/                     # Package Manager dependencies
├── ProjectSettings/              # Unity project settings
└── README.md                     # This file
```

## 🚀 Setup Instructions

### 1. Unity Project Setup

1. **Create New Unity Project**
   ```
   - Unity Version: 2022.3 LTS
   - Template: 3D (URP)
   - Project Name: VelocityRush
   ```

2. **Import Project Files**
   - Copy all files from the VelocityRush folder to your Unity project
   - Unity will automatically import and compile scripts

3. **Configure Project Settings**
   ```
   Player Settings:
   - Company Name: [Your Studio Name]
   - Product Name: Velocity Rush
   - Bundle Identifier: com.yourstudio.velocityrush
   - Minimum API Level: Android 24, iOS 12.0
   - Scripting Backend: IL2CPP
   - Target Architectures: ARM64
   ```

### 2. Photon Fusion Setup

1. **Download Photon Fusion**
   - Go to Unity Asset Store
   - Search for "Photon Fusion"
   - Download and import the latest version

2. **Create Photon Account**
   - Visit [Photon Engine Dashboard](https://dashboard.photonengine.com)
   - Create a free account
   - Create a new Fusion application
   - Copy your App ID

3. **Configure Photon Settings**
   ```
   Window → Photon Fusion → Fusion Hub
   - Enter your App ID
   - Select your region
   - Configure network settings
   ```

### 3. Package Dependencies

Install these packages via Window → Package Manager:

```json
{
  "com.unity.inputsystem": "1.7.0",
  "com.unity.addressables": "1.21.14",
  "com.unity.render-pipelines.universal": "14.0.8",
  "com.unity.textmeshpro": "3.0.6"
}
```

### 4. Scene Setup

The project includes three main scenes:

1. **MainMenu** - Entry point, networking setup
2. **Lobby** - Multiplayer lobby, player waiting
3. **RaceTrack** - Main racing gameplay

**Build Settings Configuration:**
```
File → Build Settings
- Add scenes in this order:
  0. MainMenu
  1. Lobby  
  2. RaceTrack
```

## 🎯 How to Play

### Main Menu
- **Quick Match** - Join a random room
- **Create Room** - Host a new game
- **Join Room** - Enter room name to join
- **Settings** - Audio and game settings

### Lobby
- Wait for other players to join
- Host can start the race when ready
- Shows current player count

### Racing
- **Mobile Controls:**
  - Right side: Accelerate (hold)
  - Left side: Brake (hold)
  - Slider: Steering
  - Nitro Button: Boost (when available)
- **Keyboard (Testing):**
  - WASD/Arrow Keys: Movement
  - Space: Brake
  - Shift: Nitro

### Power-ups
- **Nitro Boost** - Collect to instantly fill nitro meter
- **Speed Boost** - Temporary speed increase
- **Shield** - Protection from collisions

## 🔧 Building for Mobile

### Android Build

1. **Platform Setup**
   ```
   File → Build Settings → Android
   - Switch Platform
   - Player Settings:
     - Minimum API Level: 24
     - Target API Level: 33
     - Scripting Backend: IL2CPP
     - Architecture: ARM64
   ```

2. **Build Configuration**
   ```
   - Development Build: Unchecked (for release)
   - Compression Method: LZ4
   - Create symbols.zip: Checked (for debugging)
   ```

3. **Build Process**
   ```
   - Click "Build" or "Build and Run"
   - Choose output folder
   - Wait for build completion
   ```

### iOS Build

1. **Platform Setup**
   ```
   File → Build Settings → iOS
   - Switch Platform
   - Player Settings:
     - Target iOS Version: 12.0
     - Architecture: ARM64
     - Scripting Backend: IL2CPP
   ```

2. **Xcode Project**
   ```
   - Click "Build"
   - Choose output folder
   - Open generated Xcode project
   - Configure signing and provisioning
   - Build from Xcode
   ```

## 🐛 Troubleshooting

### Common Issues

**Photon Connection Failed**
```
- Check internet connection
- Verify App ID in Photon settings
- Ensure firewall allows Unity/Photon
- Try different Photon region
```

**Build Errors**
```
- Update to Unity 2022.3 LTS
- Reimport Photon Fusion package
- Clear Library folder and reimport
- Check Platform settings match requirements
```

**Performance Issues**
```
- Reduce graphics quality in settings
- Lower target frame rate to 30 FPS
- Disable unnecessary visual effects
- Test on target device specifications
```

**Input Not Working**
```
- Ensure Input System package is installed
- Check MobileInputUI is present in race scene
- Verify touch input settings
- Test with keyboard input first
```

### Debug Tools

**Network Debugging**
```
- Enable Fusion Statistics GUI
- Check Network Runner status
- Monitor player count and connection
- Use Unity Profiler for performance
```

**Console Logging**
```
- Check Unity Console for errors
- Enable verbose logging in Photon settings
- Use Debug.Log statements for custom debugging
```

## 📱 Performance Optimization

### Mobile Optimization Tips

1. **Graphics Settings**
   - Use URP for better mobile performance
   - Limit texture sizes (1024x1024 max)
   - Use texture compression (ASTC/PVRTC)
   - Reduce particle count and complexity

2. **Physics Optimization**
   - Use simplified colliders (box/sphere)
   - Limit rigidbody count
   - Optimize wheel collider settings
   - Reduce physics timestep if needed

3. **Network Optimization**
   - Limit network update frequency
   - Use delta compression
   - Reduce networked properties
   - Implement object culling

## 🎨 Customization

### Adding New Cars
1. Create car prefab with required components
2. Create CarConfiguration ScriptableObject
3. Add to car selection system
4. Configure physics and visual settings

### Creating New Tracks
1. Use TrackBuilder component
2. Create track piece prefabs
3. Configure spawn points and checkpoints
4. Test track layout and flow

### Adding Power-ups
1. Create power-up prefab
2. Implement PowerUp component
3. Add visual and audio effects
4. Configure spawn and respawn logic

## 📄 License

This project is provided as-is for educational and development purposes. 

**Third-Party Assets:**
- Photon Fusion: [Photon License](https://www.photonengine.com/en-US/Photon)
- Unity Engine: [Unity License](https://unity3d.com/legal/terms-of-service)

## 🤝 Contributing

Feel free to fork this project and submit improvements:
- Bug fixes and optimizations
- New features and power-ups
- Additional car models and tracks
- UI/UX improvements
- Performance enhancements

## 📞 Support

For technical support and questions:
- Check Unity Console for error messages
- Review Photon Fusion documentation
- Test on multiple devices and network conditions
- Use Unity Profiler for performance analysis

---

**Happy Racing! 🏁**
