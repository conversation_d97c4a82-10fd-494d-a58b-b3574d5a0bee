using UnityEngine;
using System.Collections.Generic;

namespace VelocityRush.Audio
{
    [System.Serializable]
    public class AudioClipData
    {
        public string name;
        public AudioClip clip;
        [Range(0f, 1f)]
        public float volume = 1f;
        [Range(0.1f, 3f)]
        public float pitch = 1f;
        public bool loop = false;
    }
    
    public class AudioManager : MonoBehaviour
    {
        public static AudioManager Instance { get; private set; }
        
        [Header("Audio Sources")]
        public AudioSource musicSource;
        public AudioSource sfxSource;
        public AudioSource engineSource;
        
        [Header("Music Clips")]
        public AudioClipData[] musicClips;
        
        [Header("SFX Clips")]
        public AudioClipData[] sfxClips;
        
        [Header("Engine Sounds")]
        public AudioClipData[] engineSounds;
        
        [Header("Settings")]
        [Range(0f, 1f)]
        public float masterVolume = 1f;
        [Range(0f, 1f)]
        public float musicVolume = 0.7f;
        [Range(0f, 1f)]
        public float sfxVolume = 1f;
        
        private Dictionary<string, AudioClipData> _musicDict = new Dictionary<string, AudioClipData>();
        private Dictionary<string, AudioClipData> _sfxDict = new Dictionary<string, AudioClipData>();
        private Dictionary<string, AudioClipData> _engineDict = new Dictionary<string, AudioClipData>();
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeAudio();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeAudio()
        {
            // Create audio sources if they don't exist
            if (musicSource == null)
            {
                GameObject musicGO = new GameObject("MusicSource");
                musicGO.transform.SetParent(transform);
                musicSource = musicGO.AddComponent<AudioSource>();
                musicSource.loop = true;
                musicSource.playOnAwake = false;
            }
            
            if (sfxSource == null)
            {
                GameObject sfxGO = new GameObject("SFXSource");
                sfxGO.transform.SetParent(transform);
                sfxSource = sfxGO.AddComponent<AudioSource>();
                sfxSource.loop = false;
                sfxSource.playOnAwake = false;
            }
            
            if (engineSource == null)
            {
                GameObject engineGO = new GameObject("EngineSource");
                engineGO.transform.SetParent(transform);
                engineSource = engineGO.AddComponent<AudioSource>();
                engineSource.loop = true;
                engineSource.playOnAwake = false;
            }
            
            // Build dictionaries
            BuildAudioDictionaries();
            
            // Load saved settings
            LoadAudioSettings();
        }
        
        private void BuildAudioDictionaries()
        {
            _musicDict.Clear();
            foreach (var clip in musicClips)
            {
                if (!string.IsNullOrEmpty(clip.name) && clip.clip != null)
                {
                    _musicDict[clip.name] = clip;
                }
            }
            
            _sfxDict.Clear();
            foreach (var clip in sfxClips)
            {
                if (!string.IsNullOrEmpty(clip.name) && clip.clip != null)
                {
                    _sfxDict[clip.name] = clip;
                }
            }
            
            _engineDict.Clear();
            foreach (var clip in engineSounds)
            {
                if (!string.IsNullOrEmpty(clip.name) && clip.clip != null)
                {
                    _engineDict[clip.name] = clip;
                }
            }
        }
        
        public void PlayMusic(string clipName)
        {
            if (_musicDict.TryGetValue(clipName, out AudioClipData clipData))
            {
                musicSource.clip = clipData.clip;
                musicSource.volume = clipData.volume * musicVolume * masterVolume;
                musicSource.pitch = clipData.pitch;
                musicSource.loop = clipData.loop;
                musicSource.Play();
            }
            else
            {
                Debug.LogWarning($"Music clip '{clipName}' not found!");
            }
        }
        
        public void PlaySFX(string clipName)
        {
            if (_sfxDict.TryGetValue(clipName, out AudioClipData clipData))
            {
                sfxSource.volume = clipData.volume * sfxVolume * masterVolume;
                sfxSource.pitch = clipData.pitch;
                sfxSource.PlayOneShot(clipData.clip);
            }
            else
            {
                Debug.LogWarning($"SFX clip '{clipName}' not found!");
            }
        }
        
        public void PlayEngine(string clipName)
        {
            if (_engineDict.TryGetValue(clipName, out AudioClipData clipData))
            {
                engineSource.clip = clipData.clip;
                engineSource.volume = clipData.volume * sfxVolume * masterVolume;
                engineSource.pitch = clipData.pitch;
                engineSource.loop = clipData.loop;
                engineSource.Play();
            }
            else
            {
                Debug.LogWarning($"Engine sound '{clipName}' not found!");
            }
        }
        
        public void StopMusic()
        {
            musicSource.Stop();
        }
        
        public void StopSFX()
        {
            sfxSource.Stop();
        }
        
        public void StopEngine()
        {
            engineSource.Stop();
        }
        
        public void SetMasterVolume(float volume)
        {
            masterVolume = Mathf.Clamp01(volume);
            UpdateAllVolumes();
            SaveAudioSettings();
        }
        
        public void SetMusicVolume(float volume)
        {
            musicVolume = Mathf.Clamp01(volume);
            UpdateMusicVolume();
            SaveAudioSettings();
        }
        
        public void SetSFXVolume(float volume)
        {
            sfxVolume = Mathf.Clamp01(volume);
            UpdateSFXVolume();
            SaveAudioSettings();
        }
        
        private void UpdateAllVolumes()
        {
            UpdateMusicVolume();
            UpdateSFXVolume();
        }
        
        private void UpdateMusicVolume()
        {
            if (musicSource.clip != null)
            {
                string currentClip = GetCurrentMusicClipName();
                if (!string.IsNullOrEmpty(currentClip) && _musicDict.TryGetValue(currentClip, out AudioClipData clipData))
                {
                    musicSource.volume = clipData.volume * musicVolume * masterVolume;
                }
            }
        }
        
        private void UpdateSFXVolume()
        {
            sfxSource.volume = sfxVolume * masterVolume;
            engineSource.volume = sfxVolume * masterVolume;
        }
        
        private string GetCurrentMusicClipName()
        {
            if (musicSource.clip == null) return null;
            
            foreach (var kvp in _musicDict)
            {
                if (kvp.Value.clip == musicSource.clip)
                {
                    return kvp.Key;
                }
            }
            
            return null;
        }
        
        private void SaveAudioSettings()
        {
            PlayerPrefs.SetFloat("MasterVolume", masterVolume);
            PlayerPrefs.SetFloat("MusicVolume", musicVolume);
            PlayerPrefs.SetFloat("SFXVolume", sfxVolume);
            PlayerPrefs.Save();
        }
        
        private void LoadAudioSettings()
        {
            masterVolume = PlayerPrefs.GetFloat("MasterVolume", 1f);
            musicVolume = PlayerPrefs.GetFloat("MusicVolume", 0.7f);
            sfxVolume = PlayerPrefs.GetFloat("SFXVolume", 1f);
            
            UpdateAllVolumes();
        }
        
        // Scene-specific audio management
        public void PlayMenuMusic()
        {
            PlayMusic("MenuTheme");
        }
        
        public void PlayRaceMusic()
        {
            PlayMusic("RaceTheme");
        }
        
        public void PlayButtonClick()
        {
            PlaySFX("ButtonClick");
        }
        
        public void PlayCarEngine(string carType = "Default")
        {
            PlayEngine($"Engine_{carType}");
        }
        
        public void PlayNitroSound()
        {
            PlaySFX("NitroBoost");
        }
        
        public void PlayCollisionSound()
        {
            PlaySFX("Collision");
        }
        
        public void PlayCheckpointSound()
        {
            PlaySFX("Checkpoint");
        }
        
        public void PlayVictorySound()
        {
            PlaySFX("Victory");
        }
        
        public void PlayDefeatSound()
        {
            PlaySFX("Defeat");
        }
    }
}
