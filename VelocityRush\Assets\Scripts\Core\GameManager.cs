using UnityEngine;
using UnityEngine.SceneManagement;

namespace VelocityRush.Core
{
    public class GameManager : MonoBehaviour
    {
        public static GameManager Instance { get; private set; }
        
        [Header("Game Settings")]
        public int maxPlayersPerRoom = 6;
        public float raceCountdownTime = 3f;
        public int totalLaps = 3;
        
        [Header("Scene Names")]
        public string mainMenuScene = "MainMenu";
        public string lobbyScene = "Lobby";
        public string raceScene = "RaceTrack";
        
        public bool IsInRace { get; private set; }
        public bool IsGamePaused { get; private set; }
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeGame()
        {
            // Set target frame rate for mobile
            Application.targetFrameRate = 60;
            
            // Don't allow screen to sleep during gameplay
            Screen.sleepTimeout = SleepTimeout.NeverSleep;
            
            // Initialize audio settings
            AudioListener.volume = 1f;
        }
        
        public void StartRace()
        {
            IsInRace = true;
            Debug.Log("Race Started!");
        }
        
        public void EndRace()
        {
            IsInRace = false;
            Debug.Log("Race Ended!");
        }
        
        public void PauseGame()
        {
            IsGamePaused = true;
            Time.timeScale = 0f;
        }
        
        public void ResumeGame()
        {
            IsGamePaused = false;
            Time.timeScale = 1f;
        }
        
        public void LoadScene(string sceneName)
        {
            SceneManager.LoadScene(sceneName);
        }
        
        public void QuitGame()
        {
            Application.Quit();
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && IsInRace)
            {
                PauseGame();
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && IsInRace)
            {
                PauseGame();
            }
        }
    }
}
