using UnityEngine;
using Fusion;
using VelocityRush.Player;

namespace VelocityRush.Race
{
    public class Checkpoint : NetworkBehaviour
    {
        [Header("Checkpoint Settings")]
        public int checkpointIndex = 0;
        public bool isFinishLine = false;
        public bool isStartLine = false;
        
        [Header("Visual Feedback")]
        public GameObject checkpointVisual;
        public ParticleSystem passEffect;
        public AudioClip passSound;
        
        private AudioSource _audioSource;
        
        private void Start()
        {
            _audioSource = GetComponent<AudioSource>();
            
            // Setup trigger collider
            var collider = GetComponent<Collider>();
            if (collider != null)
            {
                collider.isTrigger = true;
            }
        }
        
        public void OnPlayerPass(PlayerController player)
        {
            if (!Object.HasStateAuthority) return;
            
            // Validate checkpoint order (simplified)
            if (ValidateCheckpointOrder(player))
            {
                ProcessCheckpoint(player);
            }
        }
        
        private bool ValidateCheckpointOrder(PlayerController player)
        {
            // For now, accept all checkpoint passes
            // In a full implementation, you'd track the last checkpoint passed
            return true;
        }
        
        private void ProcessCheckpoint(PlayerController player)
        {
            if (isFinishLine)
            {
                ProcessFinishLine(player);
            }
            else
            {
                ProcessRegularCheckpoint(player);
            }
            
            // Play visual and audio feedback
            PlayPassEffects();
        }
        
        private void ProcessFinishLine(PlayerController player)
        {
            // Increment lap count
            int newLapCount = player.NetworkLapCount + 1;
            player.OnLapCompleted(newLapCount);
            
            Debug.Log($"Player {player.NetworkPlayerName} completed lap {newLapCount}");
        }
        
        private void ProcessRegularCheckpoint(PlayerController player)
        {
            Debug.Log($"Player {player.NetworkPlayerName} passed checkpoint {checkpointIndex}");
        }
        
        private void PlayPassEffects()
        {
            // Play particle effect
            if (passEffect != null)
            {
                passEffect.Play();
            }
            
            // Play sound effect
            if (_audioSource != null && passSound != null)
            {
                _audioSource.PlayOneShot(passSound);
            }
        }
        
        private void OnTriggerEnter(Collider other)
        {
            var player = other.GetComponent<PlayerController>();
            if (player != null)
            {
                OnPlayerPass(player);
            }
        }
        
        private void OnDrawGizmos()
        {
            // Draw checkpoint visualization in editor
            Gizmos.color = isFinishLine ? Color.green : Color.yellow;
            Gizmos.DrawWireCube(transform.position, transform.localScale);
            
            if (isFinishLine)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawLine(
                    transform.position - transform.right * 5f,
                    transform.position + transform.right * 5f
                );
            }
        }
    }
}
