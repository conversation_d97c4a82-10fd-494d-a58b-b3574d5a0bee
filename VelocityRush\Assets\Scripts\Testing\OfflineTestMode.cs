using UnityEngine;
using UnityEngine.SceneManagement;
using VelocityRush.Core;

namespace VelocityRush.Testing
{
    /// <summary>
    /// Simple offline test mode for testing car physics and race logic without networking
    /// </summary>
    public class OfflineTestMode : MonoBehaviour
    {
        [Header("Test Settings")]
        public bool enableOfflineMode = true;
        public GameObject localCarPrefab;
        public Transform[] testSpawnPoints;
        
        [Header("AI Cars (Optional)")]
        public GameObject aiCarPrefab;
        public int numberOfAICars = 3;
        
        private void Start()
        {
            if (enableOfflineMode)
            {
                SetupOfflineTest();
            }
        }
        
        private void SetupOfflineTest()
        {
            Debug.Log("Starting Offline Test Mode");
            
            // Skip networking and go straight to race
            if (SceneManager.GetActiveScene().name == "MainMenu")
            {
                // Auto-load race scene for testing
                Invoke(nameof(LoadRaceScene), 1f);
                return;
            }
            
            if (SceneManager.GetActiveScene().name == "RaceTrack")
            {
                SetupOfflineRace();
            }
        }
        
        private void LoadRaceScene()
        {
            SceneManager.LoadScene("RaceTrack");
        }
        
        private void SetupOfflineRace()
        {
            // Spawn local player car
            SpawnLocalPlayer();
            
            // Spawn AI cars if enabled
            if (numberOfAICars > 0)
            {
                SpawnAICars();
            }
            
            // Start race immediately
            if (GameManager.Instance != null)
            {
                GameManager.Instance.StartRace();
            }
            
            // Setup simple race timer
            StartCoroutine(SimpleRaceTimer());
        }
        
        private void SpawnLocalPlayer()
        {
            Vector3 spawnPos = GetSpawnPosition(0);
            
            if (localCarPrefab != null)
            {
                GameObject playerCar = Instantiate(localCarPrefab, spawnPos, Quaternion.identity);
                
                // Enable local controls
                var carMovement = playerCar.GetComponent<VelocityRush.Player.CarMovement>();
                if (carMovement != null)
                {
                    // Add simple input handler for testing
                    playerCar.AddComponent<SimpleCarInput>();
                }
                
                // Setup camera
                var camera = playerCar.GetComponentInChildren<Camera>();
                if (camera != null)
                {
                    camera.enabled = true;
                }
                
                Debug.Log("Local player car spawned for testing");
            }
        }
        
        private void SpawnAICars()
        {
            for (int i = 1; i <= numberOfAICars; i++)
            {
                Vector3 spawnPos = GetSpawnPosition(i);
                
                if (aiCarPrefab != null)
                {
                    GameObject aiCar = Instantiate(aiCarPrefab, spawnPos, Quaternion.identity);
                    
                    // Add simple AI controller
                    aiCar.AddComponent<SimpleAI>();
                    
                    // Disable camera for AI cars
                    var camera = aiCar.GetComponentInChildren<Camera>();
                    if (camera != null)
                    {
                        camera.enabled = false;
                    }
                    
                    Debug.Log($"AI car {i} spawned for testing");
                }
            }
        }
        
        private Vector3 GetSpawnPosition(int index)
        {
            if (testSpawnPoints != null && testSpawnPoints.Length > 0)
            {
                int spawnIndex = index % testSpawnPoints.Length;
                return testSpawnPoints[spawnIndex].position;
            }
            
            // Default spawn positions
            return new Vector3(index * 5f, 1f, 0f);
        }
        
        private System.Collections.IEnumerator SimpleRaceTimer()
        {
            float raceTime = 0f;
            
            while (true)
            {
                raceTime += Time.deltaTime;
                
                // Simple race time display
                if (raceTime % 10f < 0.1f) // Every 10 seconds
                {
                    Debug.Log($"Race Time: {raceTime:F1}s");
                }
                
                yield return null;
            }
        }
    }
    
    /// <summary>
    /// Simple input handler for offline testing
    /// </summary>
    public class SimpleCarInput : MonoBehaviour
    {
        private VelocityRush.Player.CarMovement carMovement;
        
        private void Start()
        {
            carMovement = GetComponent<VelocityRush.Player.CarMovement>();
        }
        
        private void Update()
        {
            if (carMovement == null) return;
            
            // Get input
            float motor = Input.GetAxis("Vertical");
            float steering = Input.GetAxis("Horizontal");
            bool brake = Input.GetKey(KeyCode.Space);
            bool nitro = Input.GetKey(KeyCode.LeftShift);
            
            // Apply to car
            carMovement.SetInputs(motor, steering, brake, nitro);
        }
    }
    
    /// <summary>
    /// Simple AI for testing
    /// </summary>
    public class SimpleAI : MonoBehaviour
    {
        private VelocityRush.Player.CarMovement carMovement;
        private float randomSteering = 0f;
        private float steeringChangeTimer = 0f;
        
        private void Start()
        {
            carMovement = GetComponent<VelocityRush.Player.CarMovement>();
        }
        
        private void Update()
        {
            if (carMovement == null) return;
            
            // Simple AI: Always accelerate, random steering
            steeringChangeTimer += Time.deltaTime;
            
            if (steeringChangeTimer > 2f) // Change direction every 2 seconds
            {
                randomSteering = Random.Range(-0.5f, 0.5f);
                steeringChangeTimer = 0f;
            }
            
            // Apply AI input
            carMovement.SetInputs(
                1f,              // Always accelerate
                randomSteering,  // Random steering
                false,           // No braking
                Random.value < 0.1f // 10% chance to use nitro
            );
        }
    }
}
