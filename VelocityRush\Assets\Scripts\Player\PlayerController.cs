using UnityEngine;
using Fusion;
using VelocityRush.UI;

namespace VelocityRush.Player
{
    public struct NetworkInputData : INetworkInput
    {
        public float motorInput;
        public float steerInput;
        public bool brakeInput;
        public bool nitroInput;
        public NetworkButtons buttons;
    }
    
    public class PlayerController : NetworkBehaviour
    {
        [Header("Player Settings")]
        public string playerName = "Player";
        public Color carColor = Color.blue;
        
        [Header("Components")]
        public CarMovement carMovement;
        public Camera playerCamera;
        public Canvas playerUI;
        
        [Header("Nitro Settings")]
        public float nitroDuration = 3f;
        public float nitroCooldown = 10f;
        
        [Networked] public string NetworkPlayerName { get; set; }
        [Networked] public Color NetworkCarColor { get; set; }
        [Networked] public int NetworkLapCount { get; set; }
        [Networked] public float NetworkRaceTime { get; set; }
        [Networked] public bool NetworkRaceFinished { get; set; }
        [Networked] public float NetworkNitroTimer { get; set; }
        [Networked] public float NetworkNitroCooldownTimer { get; set; }
        
        private bool _isLocalPlayer;
        private MobileInputUI _mobileInput;
        
        public bool IsNitroAvailable => NetworkNitroCooldownTimer <= 0f;
        public bool IsNitroActive => NetworkNitroTimer > 0f;
        public float NitroProgress => Mathf.Clamp01(1f - (NetworkNitroCooldownTimer / nitroCooldown));
        
        public override void Spawned()
        {
            _isLocalPlayer = Object.HasInputAuthority;
            
            // Set up camera and UI for local player only
            if (_isLocalPlayer)
            {
                SetupLocalPlayer();
            }
            else
            {
                SetupRemotePlayer();
            }
            
            // Initialize networked properties
            if (Object.HasStateAuthority)
            {
                NetworkPlayerName = playerName;
                NetworkCarColor = carColor;
                NetworkLapCount = 0;
                NetworkRaceTime = 0f;
                NetworkRaceFinished = false;
                NetworkNitroTimer = 0f;
                NetworkNitroCooldownTimer = 0f;
            }
            
            // Apply car color
            ApplyCarColor();
        }
        
        private void SetupLocalPlayer()
        {
            // Enable camera for local player
            if (playerCamera != null)
            {
                playerCamera.enabled = true;
                playerCamera.GetComponent<AudioListener>().enabled = true;
            }
            
            // Enable UI for local player
            if (playerUI != null)
            {
                playerUI.enabled = true;
            }
            
            // Find mobile input UI
            _mobileInput = FindObjectOfType<MobileInputUI>();
            
            Debug.Log("Local player setup complete");
        }
        
        private void SetupRemotePlayer()
        {
            // Disable camera and audio listener for remote players
            if (playerCamera != null)
            {
                playerCamera.enabled = false;
                playerCamera.GetComponent<AudioListener>().enabled = false;
            }
            
            // Disable UI for remote players
            if (playerUI != null)
            {
                playerUI.enabled = false;
            }
        }
        
        public override void FixedUpdateNetwork()
        {
            // Update race time
            if (!NetworkRaceFinished && Object.HasStateAuthority)
            {
                NetworkRaceTime += Runner.DeltaTime;
            }
            
            // Update nitro timers
            if (Object.HasStateAuthority)
            {
                UpdateNitroTimers();
            }
            
            // Handle input for local player
            if (Object.HasInputAuthority)
            {
                HandleInput();
            }
        }
        
        private void UpdateNitroTimers()
        {
            if (NetworkNitroTimer > 0f)
            {
                NetworkNitroTimer -= Runner.DeltaTime;
                if (NetworkNitroTimer <= 0f)
                {
                    NetworkNitroTimer = 0f;
                    NetworkNitroCooldownTimer = nitroCooldown;
                }
            }
            
            if (NetworkNitroCooldownTimer > 0f)
            {
                NetworkNitroCooldownTimer -= Runner.DeltaTime;
                if (NetworkNitroCooldownTimer <= 0f)
                {
                    NetworkNitroCooldownTimer = 0f;
                }
            }
        }
        
        private void HandleInput()
        {
            if (Runner.TryGetInputForCurrentTick<NetworkInputData>(out var input))
            {
                // Pass input to car movement
                if (carMovement != null)
                {
                    carMovement.SetInputs(
                        input.motorInput,
                        input.steerInput,
                        input.brakeInput,
                        IsNitroActive
                    );
                }
            }
        }
        
        public override void Render()
        {
            // Update UI elements that need frequent updates
            if (_isLocalPlayer && _mobileInput != null)
            {
                _mobileInput.UpdateNitroButton(IsNitroAvailable, NitroProgress);
                _mobileInput.UpdateSpeedometer(carMovement != null ? carMovement.CurrentSpeed : 0f);
            }
        }
        
        [Rpc(RpcSources.InputAuthority, RpcTargets.StateAuthority)]
        public void UseNitro()
        {
            if (IsNitroAvailable && !IsNitroActive)
            {
                NetworkNitroTimer = nitroDuration;
                Debug.Log($"Player {NetworkPlayerName} used nitro!");
            }
        }
        
        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        public void OnLapCompleted(int newLapCount)
        {
            NetworkLapCount = newLapCount;
            Debug.Log($"Player {NetworkPlayerName} completed lap {newLapCount}");
            
            // Check if race is finished
            if (newLapCount >= GameManager.Instance.totalLaps)
            {
                FinishRace();
            }
        }
        
        private void FinishRace()
        {
            NetworkRaceFinished = true;
            Debug.Log($"Player {NetworkPlayerName} finished the race in {NetworkRaceTime:F2} seconds!");
            
            // Notify race manager
            var raceManager = FindObjectOfType<RaceManager>();
            if (raceManager != null)
            {
                raceManager.OnPlayerFinished(this);
            }
        }
        
        private void ApplyCarColor()
        {
            // Find car renderer and apply color
            var renderers = GetComponentsInChildren<Renderer>();
            foreach (var renderer in renderers)
            {
                if (renderer.gameObject.name.Contains("Body"))
                {
                    renderer.material.color = NetworkCarColor;
                }
            }
        }
        
        public void SetPlayerName(string newName)
        {
            if (Object.HasInputAuthority)
            {
                playerName = newName;
                if (Object.HasStateAuthority)
                {
                    NetworkPlayerName = newName;
                }
            }
        }
        
        public void SetCarColor(Color newColor)
        {
            if (Object.HasInputAuthority)
            {
                carColor = newColor;
                if (Object.HasStateAuthority)
                {
                    NetworkCarColor = newColor;
                    ApplyCarColor();
                }
            }
        }
        
        private void OnTriggerEnter(Collider other)
        {
            if (!Object.HasInputAuthority) return;
            
            // Handle checkpoint triggers
            if (other.CompareTag("Checkpoint"))
            {
                var checkpoint = other.GetComponent<Checkpoint>();
                if (checkpoint != null)
                {
                    checkpoint.OnPlayerPass(this);
                }
            }
        }
    }
}
