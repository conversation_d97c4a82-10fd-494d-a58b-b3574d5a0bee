using UnityEngine;

namespace VelocityRush.Cars
{
    [CreateAssetMenu(fileName = "CarConfiguration", menuName = "VelocityRush/Car Configuration")]
    public class CarConfiguration : ScriptableObject
    {
        [Header("Car Info")]
        public string carName = "Default Car";
        public string description = "A basic racing car";
        public Sprite carIcon;
        public GameObject carPrefab;
        
        [Header("Performance Stats")]
        [Range(0f, 100f)]
        public float speed = 50f;
        
        [Range(0f, 100f)]
        public float acceleration = 50f;
        
        [Range(0f, 100f)]
        public float handling = 50f;
        
        [Range(0f, 100f)]
        public float durability = 50f;
        
        [Header("Physics Settings")]
        public float motorForce = 1500f;
        public float brakeForce = 3000f;
        public float maxSteerAngle = 30f;
        public float maxSpeed = 50f;
        public float mass = 1200f;
        
        [Header("Visual Settings")]
        public Color[] availableColors = { Color.red, Color.blue, Color.green, Color.yellow };
        public Material[] carMaterials;
        
        [Header("Unlock Requirements")]
        public bool isUnlockedByDefault = true;
        public int unlockLevel = 1;
        public int unlockCost = 0;
        
        [Header("Audio")]
        public AudioClip engineSound;
        public AudioClip hornSound;
        
        public float GetOverallRating()
        {
            return (speed + acceleration + handling + durability) / 4f;
        }
        
        public void ApplyToCarMovement(VelocityRush.Player.CarMovement carMovement)
        {
            if (carMovement == null) return;
            
            carMovement.motorForce = motorForce;
            carMovement.brakeForce = brakeForce;
            carMovement.maxSteerAngle = maxSteerAngle;
            carMovement.maxSpeed = maxSpeed;
            
            // Apply mass to rigidbody
            var rigidbody = carMovement.GetComponent<Rigidbody>();
            if (rigidbody != null)
            {
                rigidbody.mass = mass;
            }
        }
    }
}
