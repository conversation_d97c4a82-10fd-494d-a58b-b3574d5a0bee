using UnityEngine;
using System.Collections.Generic;

namespace VelocityRush.Track
{
    [System.Serializable]
    public class TrackPiece
    {
        public GameObject prefab;
        public Vector3 connectionPoint;
        public Vector3 exitPoint;
        public float length;
    }
    
    public class TrackBuilder : MonoBehaviour
    {
        [Header("Track Pieces")]
        public TrackPiece[] straightPieces;
        public TrackPiece[] curvePieces;
        public TrackPiece startLinePiece;
        public TrackPiece finishLinePiece;
        
        [Header("Track Generation")]
        public int trackLength = 20;
        public float trackWidth = 10f;
        public bool generateOnStart = true;
        public bool closeLoop = true;
        
        [Header("Spawn Points")]
        public Transform[] playerSpawnPoints;
        public float spawnSpacing = 3f;
        
        private List<GameObject> _spawnedPieces = new List<GameObject>();
        private List<Vector3> _checkpointPositions = new List<Vector3>();
        
        private void Start()
        {
            if (generateOnStart)
            {
                GenerateTrack();
            }
        }
        
        [ContextMenu("Generate Track")]
        public void GenerateTrack()
        {
            ClearTrack();
            
            Vector3 currentPosition = transform.position;
            Vector3 currentDirection = transform.forward;
            
            // Place start line
            if (startLinePiece != null && startLinePiece.prefab != null)
            {
                GameObject startLine = Instantiate(startLinePiece.prefab, currentPosition, Quaternion.LookRotation(currentDirection));
                startLine.transform.SetParent(transform);
                _spawnedPieces.Add(startLine);
                
                currentPosition += currentDirection * startLinePiece.length;
                _checkpointPositions.Add(currentPosition);
            }
            
            // Generate track pieces
            for (int i = 0; i < trackLength; i++)
            {
                TrackPiece piece = GetRandomTrackPiece();
                if (piece != null && piece.prefab != null)
                {
                    GameObject trackPiece = Instantiate(piece.prefab, currentPosition, Quaternion.LookRotation(currentDirection));
                    trackPiece.transform.SetParent(transform);
                    _spawnedPieces.Add(trackPiece);
                    
                    // Update position and direction
                    currentPosition += currentDirection * piece.length;
                    
                    // Add some randomness to direction for curves
                    if (piece == curvePieces[0] || (curvePieces.Length > 0 && System.Array.IndexOf(curvePieces, piece) >= 0))
                    {
                        float turnAngle = Random.Range(-45f, 45f);
                        currentDirection = Quaternion.Euler(0, turnAngle, 0) * currentDirection;
                    }
                    
                    // Add checkpoint every few pieces
                    if (i % 5 == 0)
                    {
                        _checkpointPositions.Add(currentPosition);
                    }
                }
            }
            
            // Place finish line (same as start for loop)
            if (closeLoop && finishLinePiece != null && finishLinePiece.prefab != null)
            {
                // Calculate direction back to start
                Vector3 directionToStart = (transform.position - currentPosition).normalized;
                GameObject finishLine = Instantiate(finishLinePiece.prefab, currentPosition, Quaternion.LookRotation(directionToStart));
                finishLine.transform.SetParent(transform);
                _spawnedPieces.Add(finishLine);
            }
            
            // Generate spawn points
            GenerateSpawnPoints();
            
            // Generate checkpoints
            GenerateCheckpoints();
            
            Debug.Log($"Generated track with {_spawnedPieces.Count} pieces and {_checkpointPositions.Count} checkpoints");
        }
        
        [ContextMenu("Clear Track")]
        public void ClearTrack()
        {
            foreach (GameObject piece in _spawnedPieces)
            {
                if (piece != null)
                {
                    DestroyImmediate(piece);
                }
            }
            
            _spawnedPieces.Clear();
            _checkpointPositions.Clear();
        }
        
        private TrackPiece GetRandomTrackPiece()
        {
            // 70% chance for straight, 30% for curve
            if (Random.value < 0.7f && straightPieces.Length > 0)
            {
                return straightPieces[Random.Range(0, straightPieces.Length)];
            }
            else if (curvePieces.Length > 0)
            {
                return curvePieces[Random.Range(0, curvePieces.Length)];
            }
            else if (straightPieces.Length > 0)
            {
                return straightPieces[Random.Range(0, straightPieces.Length)];
            }
            
            return null;
        }
        
        private void GenerateSpawnPoints()
        {
            if (playerSpawnPoints == null || playerSpawnPoints.Length == 0)
            {
                // Create spawn points near the start
                Vector3 startPosition = transform.position;
                Vector3 rightDirection = transform.right;
                
                for (int i = 0; i < 6; i++) // Max 6 players
                {
                    GameObject spawnPoint = new GameObject($"SpawnPoint_{i}");
                    spawnPoint.transform.SetParent(transform);
                    
                    // Arrange in 2 rows
                    int row = i / 3;
                    int col = i % 3;
                    
                    Vector3 spawnPos = startPosition + 
                                      rightDirection * (col - 1) * spawnSpacing +
                                      -transform.forward * row * spawnSpacing * 2f;
                    
                    spawnPoint.transform.position = spawnPos;
                    spawnPoint.transform.rotation = transform.rotation;
                }
            }
        }
        
        private void GenerateCheckpoints()
        {
            for (int i = 0; i < _checkpointPositions.Count; i++)
            {
                GameObject checkpoint = new GameObject($"Checkpoint_{i}");
                checkpoint.transform.SetParent(transform);
                checkpoint.transform.position = _checkpointPositions[i];
                
                // Add checkpoint component
                var checkpointScript = checkpoint.AddComponent<VelocityRush.Race.Checkpoint>();
                checkpointScript.checkpointIndex = i;
                checkpointScript.isFinishLine = (i == 0); // First checkpoint is finish line
                
                // Add trigger collider
                var collider = checkpoint.AddComponent<BoxCollider>();
                collider.isTrigger = true;
                collider.size = new Vector3(trackWidth, 5f, 2f);
                
                // Add visual indicator
                GameObject visual = GameObject.CreatePrimitive(PrimitiveType.Cube);
                visual.transform.SetParent(checkpoint.transform);
                visual.transform.localPosition = Vector3.zero;
                visual.transform.localScale = new Vector3(trackWidth, 0.1f, 2f);
                
                // Set checkpoint color
                var renderer = visual.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material.color = checkpointScript.isFinishLine ? Color.green : Color.yellow;
                }
                
                // Remove collider from visual (we want only the trigger)
                DestroyImmediate(visual.GetComponent<Collider>());
            }
        }
        
        public Vector3[] GetSpawnPositions()
        {
            List<Vector3> positions = new List<Vector3>();
            
            if (playerSpawnPoints != null)
            {
                foreach (Transform spawn in playerSpawnPoints)
                {
                    if (spawn != null)
                    {
                        positions.Add(spawn.position);
                    }
                }
            }
            
            // If no spawn points defined, create default ones
            if (positions.Count == 0)
            {
                Vector3 startPos = transform.position;
                for (int i = 0; i < 6; i++)
                {
                    positions.Add(startPos + Vector3.right * i * spawnSpacing);
                }
            }
            
            return positions.ToArray();
        }
        
        private void OnDrawGizmos()
        {
            // Draw track path
            Gizmos.color = Color.blue;
            for (int i = 0; i < _checkpointPositions.Count - 1; i++)
            {
                Gizmos.DrawLine(_checkpointPositions[i], _checkpointPositions[i + 1]);
            }
            
            // Draw spawn points
            Gizmos.color = Color.green;
            Vector3[] spawnPositions = GetSpawnPositions();
            foreach (Vector3 pos in spawnPositions)
            {
                Gizmos.DrawWireSphere(pos, 1f);
            }
        }
    }
}
