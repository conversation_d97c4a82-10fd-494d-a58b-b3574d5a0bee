using UnityEngine;
using Fusion;
using VelocityRush.Player;
using VelocityRush.UI;

namespace VelocityRush.Network
{
    public class FusionInputHandler : MonoBehaviour, INetworkBehaviour
    {
        private MobileInputUI _mobileInput;
        private bool _initialized = false;
        
        private void Start()
        {
            InitializeInput();
        }
        
        private void InitializeInput()
        {
            _mobileInput = FindObjectOfType<MobileInputUI>();
            _initialized = true;
        }
        
        public void OnInput(NetworkRunner runner, NetworkInputData input)
        {
            if (!_initialized)
            {
                InitializeInput();
            }
            
            var data = new NetworkInputData();
            
            if (_mobileInput != null)
            {
                // Get input from mobile UI
                data = _mobileInput.GetNetworkInput();
            }
            else
            {
                // Fallback to keyboard input for testing
                data.motorInput = Input.GetAxis("Vertical");
                data.steerInput = Input.GetAxis("Horizontal");
                data.brakeInput = Input.GetKey(KeyCode.Space);
                data.nitroInput = Input.GetKeyDown(KeyCode.LeftShift);
            }
            
            input.Set(data);
        }
        
        public void OnInputMissing(NetworkRunner runner, PlayerRef player, NetworkInputData input)
        {
            // <PERSON>le missing input - use last known input or default values
            input.Set(new NetworkInputData());
        }
        
        public void OnPlayerJoined(NetworkRunner runner, PlayerRef player)
        {
            Debug.Log($"Player {player} joined the game");
        }
        
        public void OnPlayerLeft(NetworkRunner runner, PlayerRef player)
        {
            Debug.Log($"Player {player} left the game");
        }
        
        public void OnShutdown(NetworkRunner runner, ShutdownReason shutdownReason)
        {
            Debug.Log($"Network shutdown: {shutdownReason}");
        }
        
        public void OnConnectedToServer(NetworkRunner runner)
        {
            Debug.Log("Connected to Fusion server");
        }
        
        public void OnDisconnectedFromServer(NetworkRunner runner)
        {
            Debug.Log("Disconnected from Fusion server");
        }
        
        public void OnConnectRequest(NetworkRunner runner, NetworkRunnerCallbackArgs.ConnectRequest request, byte[] token)
        {
            request.Accept();
        }
        
        public void OnConnectFailed(NetworkRunner runner, NetAddress remoteAddress, NetConnectFailedReason reason)
        {
            Debug.LogError($"Connection failed: {reason}");
        }
        
        public void OnUserSimulationMessage(NetworkRunner runner, SimulationMessagePtr message)
        {
            // Handle custom simulation messages
        }
        
        public void OnSessionListUpdated(NetworkRunner runner, System.Collections.Generic.List<SessionInfo> sessionList)
        {
            Debug.Log($"Session list updated: {sessionList.Count} sessions available");
        }
        
        public void OnCustomAuthenticationResponse(NetworkRunner runner, System.Collections.Generic.Dictionary<string, object> data)
        {
            // Handle authentication response
        }
        
        public void OnHostMigration(NetworkRunner runner, HostMigrationToken hostMigrationToken)
        {
            Debug.Log("Host migration occurred");
        }
        
        public void OnReliableDataReceived(NetworkRunner runner, PlayerRef player, System.ArraySegment<byte> data)
        {
            // Handle reliable data
        }
        
        public void OnSceneLoadDone(NetworkRunner runner)
        {
            Debug.Log("Scene load completed");
        }
        
        public void OnSceneLoadStart(NetworkRunner runner)
        {
            Debug.Log("Scene load started");
        }
    }
}
