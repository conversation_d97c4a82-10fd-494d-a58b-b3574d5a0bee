using UnityEngine;
using Fusion;
using System.Collections.Generic;
using System.Linq;
using VelocityRush.Player;
using VelocityRush.Core;

namespace VelocityRush.Race
{
    public enum RaceState
    {
        Waiting,
        Countdown,
        Racing,
        Finished
    }
    
    public class RaceManager : NetworkBehaviour
    {
        [Header("Race Settings")]
        public Transform[] spawnPoints;
        public float countdownTime = 3f;
        public int totalLaps = 3;
        
        [Header("UI References")]
        public GameObject countdownUI;
        public GameObject raceUI;
        public GameObject finishUI;
        
        [Networked] public RaceState CurrentRaceState { get; set; }
        [Networked] public float CountdownTimer { get; set; }
        [Networked] public float RaceStartTime { get; set; }
        
        private List<PlayerController> _players = new List<PlayerController>();
        private List<PlayerController> _finishedPlayers = new List<PlayerController>();
        
        public static RaceManager Instance { get; private set; }
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        public override void Spawned()
        {
            if (Object.HasStateAuthority)
            {
                CurrentRaceState = RaceState.Waiting;
                CountdownTimer = countdownTime;
            }
            
            // Wait for all players to join
            Invoke(nameof(StartCountdown), 2f);
        }
        
        public override void FixedUpdateNetwork()
        {
            switch (CurrentRaceState)
            {
                case RaceState.Waiting:
                    HandleWaitingState();
                    break;
                    
                case RaceState.Countdown:
                    HandleCountdownState();
                    break;
                    
                case RaceState.Racing:
                    HandleRacingState();
                    break;
                    
                case RaceState.Finished:
                    HandleFinishedState();
                    break;
            }
        }
        
        private void HandleWaitingState()
        {
            // Wait for minimum number of players
            UpdatePlayerList();
            
            if (Object.HasStateAuthority && _players.Count >= 1) // Allow single player for testing
            {
                StartCountdown();
            }
        }
        
        private void HandleCountdownState()
        {
            if (Object.HasStateAuthority)
            {
                CountdownTimer -= Runner.DeltaTime;
                
                if (CountdownTimer <= 0f)
                {
                    StartRace();
                }
            }
            
            // Update countdown UI
            UpdateCountdownUI();
        }
        
        private void HandleRacingState()
        {
            // Update race positions
            UpdateRacePositions();
            
            // Check if all players finished
            if (_finishedPlayers.Count >= _players.Count && _players.Count > 0)
            {
                EndRace();
            }
        }
        
        private void HandleFinishedState()
        {
            // Show final results
            ShowRaceResults();
        }
        
        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void StartCountdown()
        {
            if (Object.HasStateAuthority)
            {
                CurrentRaceState = RaceState.Countdown;
                CountdownTimer = countdownTime;
            }
            
            Debug.Log("Race countdown started!");
            
            // Show countdown UI
            if (countdownUI != null)
            {
                countdownUI.SetActive(true);
            }
            
            if (raceUI != null)
            {
                raceUI.SetActive(false);
            }
        }
        
        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void StartRace()
        {
            if (Object.HasStateAuthority)
            {
                CurrentRaceState = RaceState.Racing;
                RaceStartTime = (float)Runner.SimulationTime;
            }
            
            Debug.Log("Race started!");
            GameManager.Instance.StartRace();
            
            // Update UI
            if (countdownUI != null)
            {
                countdownUI.SetActive(false);
            }
            
            if (raceUI != null)
            {
                raceUI.SetActive(true);
            }
        }
        
        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void EndRace()
        {
            if (Object.HasStateAuthority)
            {
                CurrentRaceState = RaceState.Finished;
            }
            
            Debug.Log("Race finished!");
            GameManager.Instance.EndRace();
            
            // Show finish UI
            if (finishUI != null)
            {
                finishUI.SetActive(true);
            }
        }
        
        public void OnPlayerFinished(PlayerController player)
        {
            if (!_finishedPlayers.Contains(player))
            {
                _finishedPlayers.Add(player);
                int position = _finishedPlayers.Count;
                
                Debug.Log($"Player {player.NetworkPlayerName} finished in position {position}");
                
                // Award points based on position
                AwardPoints(player, position);
            }
        }
        
        private void AwardPoints(PlayerController player, int position)
        {
            int points = 0;
            switch (position)
            {
                case 1: points = 100; break;
                case 2: points = 80; break;
                case 3: points = 60; break;
                case 4: points = 40; break;
                case 5: points = 20; break;
                case 6: points = 10; break;
                default: points = 5; break;
            }
            
            Debug.Log($"Player {player.NetworkPlayerName} earned {points} points for position {position}");
        }
        
        private void UpdatePlayerList()
        {
            _players.Clear();
            var allPlayers = FindObjectsOfType<PlayerController>();
            _players.AddRange(allPlayers);
        }
        
        private void UpdateRacePositions()
        {
            if (_players.Count == 0) return;
            
            // Sort players by lap count and race progress
            _players.Sort((a, b) => {
                if (a.NetworkLapCount != b.NetworkLapCount)
                    return b.NetworkLapCount.CompareTo(a.NetworkLapCount);
                
                // If same lap, sort by distance to next checkpoint (simplified)
                return a.NetworkRaceTime.CompareTo(b.NetworkRaceTime);
            });
        }
        
        private void UpdateCountdownUI()
        {
            if (countdownUI != null)
            {
                var countdownText = countdownUI.GetComponentInChildren<UnityEngine.UI.Text>();
                if (countdownText != null)
                {
                    int countdown = Mathf.CeilToInt(CountdownTimer);
                    if (countdown > 0)
                    {
                        countdownText.text = countdown.ToString();
                    }
                    else
                    {
                        countdownText.text = "GO!";
                    }
                }
            }
        }
        
        private void ShowRaceResults()
        {
            if (finishUI != null && !finishUI.activeInHierarchy)
            {
                finishUI.SetActive(true);
                
                // Update results display
                var resultsText = finishUI.GetComponentInChildren<UnityEngine.UI.Text>();
                if (resultsText != null)
                {
                    string results = "Race Results:\n\n";
                    for (int i = 0; i < _finishedPlayers.Count; i++)
                    {
                        var player = _finishedPlayers[i];
                        results += $"{i + 1}. {player.NetworkPlayerName} - {player.NetworkRaceTime:F2}s\n";
                    }
                    resultsText.text = results;
                }
            }
        }
        
        public Vector3 GetSpawnPosition(int playerIndex)
        {
            if (spawnPoints != null && spawnPoints.Length > 0)
            {
                int spawnIndex = playerIndex % spawnPoints.Length;
                return spawnPoints[spawnIndex].position;
            }
            
            // Default spawn positions
            float spacing = 5f;
            return new Vector3(playerIndex * spacing, 1f, 0f);
        }
        
        public int GetPlayerPosition(PlayerController player)
        {
            UpdateRacePositions();
            return _players.IndexOf(player) + 1;
        }
        
        public List<PlayerController> GetLeaderboard()
        {
            UpdateRacePositions();
            return new List<PlayerController>(_players);
        }
    }
}
