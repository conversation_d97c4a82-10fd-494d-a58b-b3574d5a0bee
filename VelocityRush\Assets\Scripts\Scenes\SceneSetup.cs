using UnityEngine;
using UnityEngine.SceneManagement;
using VelocityRush.Core;
using VelocityRush.Network;
using VelocityRush.UI;

namespace VelocityRush.Scenes
{
    public class SceneSetup : MonoBehaviour
    {
        [Header("Scene Configuration")]
        public SceneType sceneType = SceneType.MainMenu;
        
        [<PERSON>er("Main Menu Setup")]
        public GameObject mainMenuUI;
        
        [Header("Lobby Setup")]
        public GameObject lobbyUI;
        
        [Header("Race Setup")]
        public GameObject raceUI;
        public GameObject trackParent;
        public Transform[] spawnPoints;
        
        private void Start()
        {
            SetupScene();
        }
        
        private void SetupScene()
        {
            switch (sceneType)
            {
                case SceneType.MainMenu:
                    SetupMainMenu();
                    break;
                    
                case SceneType.Lobby:
                    SetupLobby();
                    break;
                    
                case SceneType.Race:
                    SetupRace();
                    break;
            }
        }
        
        private void SetupMainMenu()
        {
            Debug.Log("Setting up Main Menu scene");
            
            // Ensure GameManager exists
            if (GameManager.Instance == null)
            {
                var gameManagerPrefab = Resources.Load<GameObject>("Prefabs/Managers/GameManager");
                if (gameManagerPrefab != null)
                {
                    Instantiate(gameManagerPrefab);
                }
            }
            
            // Ensure NetworkManager exists
            if (NetworkManager.Instance == null)
            {
                var networkManagerPrefab = Resources.Load<GameObject>("Prefabs/Managers/NetworkManager");
                if (networkManagerPrefab != null)
                {
                    Instantiate(networkManagerPrefab);
                }
            }
            
            // Ensure UIManager exists
            if (UIManager.Instance == null)
            {
                var uiManagerPrefab = Resources.Load<GameObject>("Prefabs/Managers/UIManager");
                if (uiManagerPrefab != null)
                {
                    Instantiate(uiManagerPrefab);
                }
            }
            
            // Show main menu UI
            if (UIManager.Instance != null)
            {
                UIManager.Instance.ShowMainMenu();
            }
        }
        
        private void SetupLobby()
        {
            Debug.Log("Setting up Lobby scene");
            
            // Show lobby UI
            if (UIManager.Instance != null)
            {
                UIManager.Instance.ShowLobby();
            }
        }
        
        private void SetupRace()
        {
            Debug.Log("Setting up Race scene");
            
            // Hide loading UI
            if (UIManager.Instance != null)
            {
                UIManager.Instance.HideLoading();
            }
            
            // Setup race environment
            SetupRaceEnvironment();
            
            // Initialize race manager
            var raceManager = FindObjectOfType<VelocityRush.Race.RaceManager>();
            if (raceManager == null)
            {
                var raceManagerPrefab = Resources.Load<GameObject>("Prefabs/Managers/RaceManager");
                if (raceManagerPrefab != null)
                {
                    Instantiate(raceManagerPrefab);
                }
            }
        }
        
        private void SetupRaceEnvironment()
        {
            // Setup lighting for race
            RenderSettings.ambientLight = Color.gray;
            RenderSettings.fog = true;
            RenderSettings.fogColor = Color.gray;
            RenderSettings.fogMode = FogMode.ExponentialSquared;
            RenderSettings.fogDensity = 0.01f;
            
            // Setup physics settings for racing
            Physics.gravity = new Vector3(0, -20f, 0); // Slightly stronger gravity for better car feel
        }
        
        public Vector3 GetSpawnPosition(int playerIndex)
        {
            if (spawnPoints != null && spawnPoints.Length > 0)
            {
                int spawnIndex = playerIndex % spawnPoints.Length;
                return spawnPoints[spawnIndex].position;
            }
            
            // Default spawn positions
            float spacing = 5f;
            return new Vector3(playerIndex * spacing, 1f, 0f);
        }
        
        public Quaternion GetSpawnRotation(int playerIndex)
        {
            if (spawnPoints != null && spawnPoints.Length > 0)
            {
                int spawnIndex = playerIndex % spawnPoints.Length;
                return spawnPoints[spawnIndex].rotation;
            }
            
            return Quaternion.identity;
        }
    }
    
    public enum SceneType
    {
        MainMenu,
        Lobby,
        Race
    }
}
