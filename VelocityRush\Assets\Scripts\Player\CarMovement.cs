using UnityEngine;
using Fusion;

namespace VelocityRush.Player
{
    public class CarMovement : NetworkBehaviour
    {
        [Header("Car Settings")]
        public float motorForce = 1500f;
        public float brakeForce = 3000f;
        public float maxSteerAngle = 30f;
        public float maxSpeed = 50f;
        
        [Header("Wheels")]
        public WheelCollider frontLeftWheelCollider;
        public WheelCollider frontRightWheelCollider;
        public WheelCollider rearLeftWheelCollider;
        public WheelCollider rearRightWheelCollider;
        
        [Header("Wheel Meshes")]
        public Transform frontLeftWheelTransform;
        public Transform frontRightWheelTransform;
        public Transform rearLeftWheelTransform;
        public Transform rearRightWheelTransform;
        
        [Header("Center of Mass")]
        public Transform centerOfMass;
        
        [Networked] public float NetworkMotorInput { get; set; }
        [Networked] public float NetworkSteerInput { get; set; }
        [Networked] public bool NetworkBrakeInput { get; set; }
        [Networked] public bool NetworkNitroActive { get; set; }
        
        private Rigidbody _rigidbody;
        private float _currentMotorForce;
        private float _currentBrakeForce;
        private float _currentSteerAngle;
        
        public float CurrentSpeed => _rigidbody.velocity.magnitude * 3.6f; // Convert to km/h
        
        private void Start()
        {
            _rigidbody = GetComponent<Rigidbody>();
            
            // Set center of mass for better car physics
            if (centerOfMass != null)
            {
                _rigidbody.centerOfMass = centerOfMass.localPosition;
            }
            else
            {
                _rigidbody.centerOfMass = new Vector3(0, -0.5f, 0);
            }
        }
        
        public override void FixedUpdateNetwork()
        {
            if (Object.HasInputAuthority)
            {
                // Get input from the player controller
                HandleInput();
            }
            
            // Apply forces to the car
            ApplyMotor();
            ApplySteering();
            ApplyBrake();
            UpdateWheelPoses();
        }
        
        private void HandleInput()
        {
            // This will be called by PlayerController
            // Input is handled there and passed to this component
        }
        
        public void SetInputs(float motor, float steering, bool brake, bool nitro)
        {
            NetworkMotorInput = motor;
            NetworkSteerInput = steering;
            NetworkBrakeInput = brake;
            NetworkNitroActive = nitro;
        }
        
        private void ApplyMotor()
        {
            _currentMotorForce = NetworkMotorInput * motorForce;
            
            // Apply nitro boost
            if (NetworkNitroActive)
            {
                _currentMotorForce *= 2f; // Double the force during nitro
            }
            
            // Limit max speed
            if (CurrentSpeed < maxSpeed)
            {
                frontLeftWheelCollider.motorTorque = _currentMotorForce;
                frontRightWheelCollider.motorTorque = _currentMotorForce;
            }
            else
            {
                frontLeftWheelCollider.motorTorque = 0;
                frontRightWheelCollider.motorTorque = 0;
            }
        }
        
        private void ApplySteering()
        {
            _currentSteerAngle = maxSteerAngle * NetworkSteerInput;
            frontLeftWheelCollider.steerAngle = _currentSteerAngle;
            frontRightWheelCollider.steerAngle = _currentSteerAngle;
        }
        
        private void ApplyBrake()
        {
            _currentBrakeForce = NetworkBrakeInput ? brakeForce : 0f;
            
            frontLeftWheelCollider.brakeTorque = _currentBrakeForce;
            frontRightWheelCollider.brakeTorque = _currentBrakeForce;
            rearLeftWheelCollider.brakeTorque = _currentBrakeForce;
            rearRightWheelCollider.brakeTorque = _currentBrakeForce;
        }
        
        private void UpdateWheelPoses()
        {
            UpdateWheelPose(frontLeftWheelCollider, frontLeftWheelTransform);
            UpdateWheelPose(frontRightWheelCollider, frontRightWheelTransform);
            UpdateWheelPose(rearLeftWheelCollider, rearLeftWheelTransform);
            UpdateWheelPose(rearRightWheelCollider, rearRightWheelTransform);
        }
        
        private void UpdateWheelPose(WheelCollider collider, Transform wheelTransform)
        {
            if (wheelTransform == null) return;
            
            Vector3 pos;
            Quaternion rot;
            collider.GetWorldPose(out pos, out rot);
            
            wheelTransform.position = pos;
            wheelTransform.rotation = rot;
        }
        
        public void ResetCar()
        {
            // Reset car position and rotation
            _rigidbody.velocity = Vector3.zero;
            _rigidbody.angularVelocity = Vector3.zero;
            
            // Find nearest track position (simplified)
            transform.position = new Vector3(transform.position.x, 1f, transform.position.z);
            transform.rotation = Quaternion.identity;
        }
        
        private void OnTriggerEnter(Collider other)
        {
            // Handle power-up collection
            if (other.CompareTag("PowerUp"))
            {
                var powerUp = other.GetComponent<PowerUp>();
                if (powerUp != null && Object.HasInputAuthority)
                {
                    powerUp.Collect();
                }
            }
        }
    }
}
