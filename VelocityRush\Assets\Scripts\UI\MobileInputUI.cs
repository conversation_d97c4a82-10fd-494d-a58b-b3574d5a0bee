using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using VelocityRush.Player;
using VelocityRush.Network;

namespace VelocityRush.UI
{
    public class MobileInputUI : MonoBehaviour
    {
        [Header("Input Controls")]
        public Button accelerateButton;
        public Button brakeButton;
        public Button nitroButton;
        public Slider steeringSlider;
        
        [Header("HUD Elements")]
        public Text speedText;
        public Text lapText;
        public Text positionText;
        public Image nitroFillImage;
        public Text nitroText;
        
        [Header("Minimap")]
        public RawImage minimapImage;
        public Transform minimapPlayerIcon;
        
        [Header("Race Info")]
        public Text raceTimeText;
        public Transform leaderboardParent;
        public GameObject leaderboardItemPrefab;
        
        private NetworkInputData _currentInput;
        private bool _isAccelerating;
        private bool _isBraking;
        private bool _nitroPressed;
        private float _steeringInput;
        
        private PlayerController _localPlayer;
        
        private void Start()
        {
            SetupInputControls();
            FindLocalPlayer();
        }
        
        private void SetupInputControls()
        {
            // Setup button events
            if (accelerateButton != null)
            {
                var acceleratePointer = accelerateButton.gameObject.AddComponent<EventTrigger>();
                
                var pointerDown = new EventTrigger.Entry();
                pointerDown.eventID = EventTriggerType.PointerDown;
                pointerDown.callback.AddListener((data) => { _isAccelerating = true; });
                acceleratePointer.triggers.Add(pointerDown);
                
                var pointerUp = new EventTrigger.Entry();
                pointerUp.eventID = EventTriggerType.PointerUp;
                pointerUp.callback.AddListener((data) => { _isAccelerating = false; });
                acceleratePointer.triggers.Add(pointerUp);
            }
            
            if (brakeButton != null)
            {
                var brakePointer = brakeButton.gameObject.AddComponent<EventTrigger>();
                
                var pointerDown = new EventTrigger.Entry();
                pointerDown.eventID = EventTriggerType.PointerDown;
                pointerDown.callback.AddListener((data) => { _isBraking = true; });
                brakePointer.triggers.Add(pointerDown);
                
                var pointerUp = new EventTrigger.Entry();
                pointerUp.eventID = EventTriggerType.PointerUp;
                pointerUp.callback.AddListener((data) => { _isBraking = false; });
                brakePointer.triggers.Add(pointerUp);
            }
            
            if (nitroButton != null)
            {
                nitroButton.onClick.AddListener(OnNitroPressed);
            }
            
            if (steeringSlider != null)
            {
                steeringSlider.onValueChanged.AddListener(OnSteeringChanged);
                steeringSlider.value = 0.5f; // Center position
            }
        }
        
        private void FindLocalPlayer()
        {
            // Find the local player controller
            var players = FindObjectsOfType<PlayerController>();
            foreach (var player in players)
            {
                if (player.Object != null && player.Object.HasInputAuthority)
                {
                    _localPlayer = player;
                    break;
                }
            }
        }
        
        private void Update()
        {
            if (_localPlayer == null)
            {
                FindLocalPlayer();
                return;
            }
            
            // Update input data
            UpdateInputData();
            
            // Update HUD
            UpdateHUD();
        }
        
        private void UpdateInputData()
        {
            // Convert steering slider to -1 to 1 range
            _steeringInput = (steeringSlider.value - 0.5f) * 2f;
            
            // Create input data
            _currentInput = new NetworkInputData
            {
                motorInput = _isAccelerating ? 1f : 0f,
                steerInput = _steeringInput,
                brakeInput = _isBraking,
                nitroInput = _nitroPressed
            };
            
            // Reset nitro press (single frame input)
            _nitroPressed = false;
        }
        
        private void UpdateHUD()
        {
            if (_localPlayer == null) return;
            
            // Update speed
            if (speedText != null && _localPlayer.carMovement != null)
            {
                float speed = _localPlayer.carMovement.CurrentSpeed;
                speedText.text = $"{speed:F0} km/h";
            }
            
            // Update lap count
            if (lapText != null)
            {
                int currentLap = _localPlayer.NetworkLapCount + 1;
                int totalLaps = GameManager.Instance.totalLaps;
                lapText.text = $"Lap: {currentLap}/{totalLaps}";
            }
            
            // Update position
            if (positionText != null)
            {
                var raceManager = FindObjectOfType<VelocityRush.Race.RaceManager>();
                if (raceManager != null)
                {
                    int position = raceManager.GetPlayerPosition(_localPlayer);
                    positionText.text = GetPositionText(position);
                }
            }
            
            // Update race time
            if (raceTimeText != null)
            {
                float raceTime = _localPlayer.NetworkRaceTime;
                raceTimeText.text = FormatTime(raceTime);
            }
        }
        
        public void UpdateNitroButton(bool available, float progress)
        {
            if (nitroButton != null)
            {
                nitroButton.interactable = available;
                
                // Update visual feedback
                var buttonImage = nitroButton.GetComponent<Image>();
                if (buttonImage != null)
                {
                    buttonImage.color = available ? Color.white : Color.gray;
                }
            }
            
            if (nitroFillImage != null)
            {
                nitroFillImage.fillAmount = progress;
            }
            
            if (nitroText != null)
            {
                if (available)
                {
                    nitroText.text = "NITRO READY";
                }
                else
                {
                    nitroText.text = $"COOLDOWN {(1f - progress) * 100f:F0}%";
                }
            }
        }
        
        public void UpdateSpeedometer(float speed)
        {
            if (speedText != null)
            {
                speedText.text = $"{speed:F0} km/h";
            }
        }
        
        private void OnNitroPressed()
        {
            if (_localPlayer != null && _localPlayer.IsNitroAvailable)
            {
                _nitroPressed = true;
                _localPlayer.UseNitro();
            }
        }
        
        private void OnSteeringChanged(float value)
        {
            // Steering is handled in UpdateInputData()
        }
        
        private string GetPositionText(int position)
        {
            switch (position)
            {
                case 1: return "1st";
                case 2: return "2nd";
                case 3: return "3rd";
                default: return $"{position}th";
            }
        }
        
        private string FormatTime(float time)
        {
            int minutes = Mathf.FloorToInt(time / 60f);
            int seconds = Mathf.FloorToInt(time % 60f);
            int milliseconds = Mathf.FloorToInt((time * 1000f) % 1000f);
            
            return $"{minutes:00}:{seconds:00}.{milliseconds:000}";
        }
        
        // Called by NetworkManager to get current input
        public NetworkInputData GetNetworkInput()
        {
            return _currentInput;
        }
        
        // Touch input for mobile devices
        private void HandleTouchInput()
        {
            if (Input.touchCount > 0)
            {
                for (int i = 0; i < Input.touchCount; i++)
                {
                    Touch touch = Input.GetTouch(i);
                    
                    // Handle touch input for steering if no UI slider is used
                    if (steeringSlider == null)
                    {
                        Vector2 touchPosition = touch.position;
                        float screenWidth = Screen.width;
                        
                        // Convert touch position to steering input
                        float normalizedX = touchPosition.x / screenWidth;
                        _steeringInput = (normalizedX - 0.5f) * 2f;
                        _steeringInput = Mathf.Clamp(_steeringInput, -1f, 1f);
                    }
                }
            }
        }
        
        // Alternative input method using touch zones
        public void SetupTouchZones()
        {
            // Create invisible touch zones for acceleration and braking
            GameObject leftZone = new GameObject("LeftTouchZone");
            GameObject rightZone = new GameObject("RightTouchZone");
            
            leftZone.transform.SetParent(transform);
            rightZone.transform.SetParent(transform);
            
            // Setup touch zone components
            var leftRect = leftZone.AddComponent<RectTransform>();
            var rightRect = rightZone.AddComponent<RectTransform>();
            
            // Position zones
            leftRect.anchorMin = new Vector2(0, 0);
            leftRect.anchorMax = new Vector2(0.5f, 1);
            leftRect.offsetMin = Vector2.zero;
            leftRect.offsetMax = Vector2.zero;
            
            rightRect.anchorMin = new Vector2(0.5f, 0);
            rightRect.anchorMax = new Vector2(1, 1);
            rightRect.offsetMin = Vector2.zero;
            rightRect.offsetMax = Vector2.zero;
            
            // Add touch handlers
            var leftTrigger = leftZone.AddComponent<EventTrigger>();
            var rightTrigger = rightZone.AddComponent<EventTrigger>();
            
            // Left zone for braking
            var leftDown = new EventTrigger.Entry();
            leftDown.eventID = EventTriggerType.PointerDown;
            leftDown.callback.AddListener((data) => { _isBraking = true; });
            leftTrigger.triggers.Add(leftDown);
            
            var leftUp = new EventTrigger.Entry();
            leftUp.eventID = EventTriggerType.PointerUp;
            leftUp.callback.AddListener((data) => { _isBraking = false; });
            leftTrigger.triggers.Add(leftUp);
            
            // Right zone for acceleration
            var rightDown = new EventTrigger.Entry();
            rightDown.eventID = EventTriggerType.PointerDown;
            rightDown.callback.AddListener((data) => { _isAccelerating = true; });
            rightTrigger.triggers.Add(rightDown);
            
            var rightUp = new EventTrigger.Entry();
            rightUp.eventID = EventTriggerType.PointerUp;
            rightUp.callback.AddListener((data) => { _isAccelerating = false; });
            rightTrigger.triggers.Add(rightUp);
        }
    }
}
