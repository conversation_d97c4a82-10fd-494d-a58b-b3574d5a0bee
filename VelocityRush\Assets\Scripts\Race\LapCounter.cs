using UnityEngine;
using Fusion;
using System.Collections.Generic;
using VelocityRush.Player;

namespace VelocityRush.Race
{
    public class LapCounter : NetworkBehaviour
    {
        [Header("Lap Settings")]
        public int totalLaps = 3;
        public float lapValidationDistance = 50f; // Minimum distance to travel for valid lap
        
        [Header("Checkpoint References")]
        public Checkpoint[] checkpoints;
        public Checkpoint finishLineCheckpoint;
        
        private Dictionary<PlayerController, PlayerLapData> _playerLapData = new Dictionary<PlayerController, PlayerLapData>();
        
        [System.Serializable]
        public class PlayerLapData
        {
            public int currentLap = 0;
            public int lastCheckpointIndex = -1;
            public float totalDistance = 0f;
            public float lapStartTime = 0f;
            public float bestLapTime = float.MaxValue;
            public List<float> lapTimes = new List<float>();
            public bool hasFinished = false;
        }
        
        public override void Spawned()
        {
            // Find all checkpoints if not assigned
            if (checkpoints == null || checkpoints.Length == 0)
            {
                checkpoints = FindObjectsOfType<Checkpoint>();
                System.Array.Sort(checkpoints, (a, b) => a.checkpointIndex.CompareTo(b.checkpointIndex));
            }
            
            // Find finish line
            if (finishLineCheckpoint == null)
            {
                foreach (var checkpoint in checkpoints)
                {
                    if (checkpoint.isFinishLine)
                    {
                        finishLineCheckpoint = checkpoint;
                        break;
                    }
                }
            }
        }
        
        public void RegisterPlayer(PlayerController player)
        {
            if (!_playerLapData.ContainsKey(player))
            {
                var lapData = new PlayerLapData();
                lapData.lapStartTime = (float)Runner.SimulationTime;
                _playerLapData[player] = lapData;
                
                Debug.Log($"Registered player {player.NetworkPlayerName} for lap counting");
            }
        }
        
        public void OnPlayerPassCheckpoint(PlayerController player, Checkpoint checkpoint)
        {
            if (!_playerLapData.ContainsKey(player))
            {
                RegisterPlayer(player);
            }
            
            var lapData = _playerLapData[player];
            
            if (lapData.hasFinished) return;
            
            // Validate checkpoint order
            if (ValidateCheckpointOrder(lapData, checkpoint))
            {
                lapData.lastCheckpointIndex = checkpoint.checkpointIndex;
                
                // Check if this is the finish line
                if (checkpoint.isFinishLine)
                {
                    ProcessLapCompletion(player, lapData);
                }
            }
        }
        
        private bool ValidateCheckpointOrder(PlayerLapData lapData, Checkpoint checkpoint)
        {
            // For finish line, player must have passed at least half the checkpoints
            if (checkpoint.isFinishLine)
            {
                int requiredCheckpoints = Mathf.Max(1, checkpoints.Length / 2);
                return lapData.lastCheckpointIndex >= requiredCheckpoints - 1;
            }
            
            // For regular checkpoints, allow some flexibility in order
            int expectedNext = lapData.lastCheckpointIndex + 1;
            int tolerance = 2; // Allow skipping 1-2 checkpoints
            
            return checkpoint.checkpointIndex >= expectedNext - tolerance &&
                   checkpoint.checkpointIndex <= expectedNext + tolerance;
        }
        
        private void ProcessLapCompletion(PlayerController player, PlayerLapData lapData)
        {
            float currentTime = (float)Runner.SimulationTime;
            float lapTime = currentTime - lapData.lapStartTime;
            
            // Validate minimum lap time (prevent cheating)
            if (lapTime < 10f && lapData.currentLap > 0) // First lap can be shorter
            {
                Debug.LogWarning($"Lap time too short for {player.NetworkPlayerName}: {lapTime}s");
                return;
            }
            
            lapData.currentLap++;
            lapData.lapTimes.Add(lapTime);
            
            // Update best lap time
            if (lapTime < lapData.bestLapTime)
            {
                lapData.bestLapTime = lapTime;
            }
            
            // Reset for next lap
            lapData.lapStartTime = currentTime;
            lapData.lastCheckpointIndex = -1;
            
            // Update player's networked lap count
            if (player.Object.HasStateAuthority)
            {
                player.NetworkLapCount = lapData.currentLap;
            }
            
            Debug.Log($"Player {player.NetworkPlayerName} completed lap {lapData.currentLap} in {lapTime:F2}s");
            
            // Check if race is finished
            if (lapData.currentLap >= totalLaps)
            {
                FinishRace(player, lapData);
            }
        }
        
        private void FinishRace(PlayerController player, PlayerLapData lapData)
        {
            lapData.hasFinished = true;
            
            if (player.Object.HasStateAuthority)
            {
                player.NetworkRaceFinished = true;
            }
            
            // Calculate total race time
            float totalRaceTime = lapData.lapTimes.Sum();
            
            Debug.Log($"Player {player.NetworkPlayerName} finished the race!");
            Debug.Log($"Total time: {totalRaceTime:F2}s, Best lap: {lapData.bestLapTime:F2}s");
            
            // Notify race manager
            var raceManager = FindObjectOfType<RaceManager>();
            if (raceManager != null)
            {
                raceManager.OnPlayerFinished(player);
            }
        }
        
        public PlayerLapData GetPlayerLapData(PlayerController player)
        {
            return _playerLapData.TryGetValue(player, out PlayerLapData data) ? data : null;
        }
        
        public int GetPlayerPosition(PlayerController player)
        {
            var allPlayers = new List<PlayerController>(_playerLapData.Keys);
            
            // Sort by lap count (descending) then by race time (ascending)
            allPlayers.Sort((a, b) => {
                var dataA = _playerLapData[a];
                var dataB = _playerLapData[b];
                
                if (dataA.currentLap != dataB.currentLap)
                    return dataB.currentLap.CompareTo(dataA.currentLap);
                
                float timeA = dataA.lapTimes.Sum();
                float timeB = dataB.lapTimes.Sum();
                return timeA.CompareTo(timeB);
            });
            
            return allPlayers.IndexOf(player) + 1;
        }
        
        public List<PlayerController> GetLeaderboard()
        {
            var allPlayers = new List<PlayerController>(_playerLapData.Keys);
            
            allPlayers.Sort((a, b) => {
                var dataA = _playerLapData[a];
                var dataB = _playerLapData[b];
                
                // Finished players first
                if (dataA.hasFinished != dataB.hasFinished)
                    return dataB.hasFinished.CompareTo(dataA.hasFinished);
                
                // Then by lap count
                if (dataA.currentLap != dataB.currentLap)
                    return dataB.currentLap.CompareTo(dataA.currentLap);
                
                // Then by total time
                float timeA = dataA.lapTimes.Sum();
                float timeB = dataB.lapTimes.Sum();
                return timeA.CompareTo(timeB);
            });
            
            return allPlayers;
        }
        
        public float GetPlayerBestLap(PlayerController player)
        {
            var data = GetPlayerLapData(player);
            return data?.bestLapTime ?? 0f;
        }
        
        public float GetPlayerTotalTime(PlayerController player)
        {
            var data = GetPlayerLapData(player);
            return data?.lapTimes.Sum() ?? 0f;
        }
        
        public bool IsPlayerFinished(PlayerController player)
        {
            var data = GetPlayerLapData(player);
            return data?.hasFinished ?? false;
        }
        
        // Clean up when players leave
        public void UnregisterPlayer(PlayerController player)
        {
            if (_playerLapData.ContainsKey(player))
            {
                _playerLapData.Remove(player);
                Debug.Log($"Unregistered player {player.NetworkPlayerName} from lap counting");
            }
        }
    }
}

// Extension method for List<float>.Sum()
public static class ListExtensions
{
    public static float Sum(this List<float> list)
    {
        float sum = 0f;
        foreach (float value in list)
        {
            sum += value;
        }
        return sum;
    }
}
